{% extends "!layout.html" %}

{%- block extrahead %}
<!-- 添加Font Awesome图标支持 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<!-- 添加自定义元标签 -->
<meta name="description" content="xROCS - Unified Robot Control System">
<meta name="keywords" content="xROCS, robot control, API, documentation">
<meta name="author" content="EAI">
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<!-- Open Graph元标签，用于社交媒体分享 -->
<meta property="og:title" content="xROCS Documentation">
<meta property="og:description" content="xROCS - Unified Robot Control System">
<meta property="og:type" content="website">
<meta property="og:image" content="_static/xrocs_logo.png">

<!-- 预加载字体以提高性能 -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">

<style>
  /* 使用中文Noto Sans SC字体 */
  body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", Arial, sans-serif;
  }
  
  /* 滚动条美化 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #1a73e8;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #0d5bba;
  }
  
  /* 平滑滚动效果 */
  html {
    scroll-behavior: smooth;
  }
  
  /* 暗色模式滚动条 */
  .dark-mode::-webkit-scrollbar-track {
    background: #2d2d2d;
  }
  
  .dark-mode::-webkit-scrollbar-thumb {
    background: #4285f4;
  }
  
  .dark-mode::-webkit-scrollbar-thumb:hover {
    background: #1a56e8;
  }
  
  /* 图片懒加载占位符 */
  img.lazy-load {
    transition: opacity 0.5s;
    opacity: 0;
  }
  
  img.lazy-loaded {
    opacity: 1;
  }
</style>

<!-- 图片懒加载脚本 -->
<script>
  document.addEventListener("DOMContentLoaded", function() {
    var lazyImages = [].slice.call(document.querySelectorAll("img.lazy-load"));
    
    if ("IntersectionObserver" in window) {
      let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            let lazyImage = entry.target;
            lazyImage.src = lazyImage.dataset.src;
            lazyImage.classList.add("lazy-loaded");
            lazyImageObserver.unobserve(lazyImage);
          }
        });
      });
      
      lazyImages.forEach(function(lazyImage) {
        lazyImageObserver.observe(lazyImage);
      });
    } else {
      // 回退机制
      let active = false;
      
      const lazyLoad = function() {
        if (active === false) {
          active = true;
          
          setTimeout(function() {
            lazyImages.forEach(function(lazyImage) {
              if ((lazyImage.getBoundingClientRect().top <= window.innerHeight && lazyImage.getBoundingClientRect().bottom >= 0) && getComputedStyle(lazyImage).display !== "none") {
                lazyImage.src = lazyImage.dataset.src;
                lazyImage.classList.add("lazy-loaded");
                
                lazyImages = lazyImages.filter(function(image) {
                  return image !== lazyImage;
                });
                
                if (lazyImages.length === 0) {
                  document.removeEventListener("scroll", lazyLoad);
                  window.removeEventListener("resize", lazyLoad);
                  window.removeEventListener("orientationchange", lazyLoad);
                }
              }
            });
            
            active = false;
          }, 200);
        }
      };
      
      document.addEventListener("scroll", lazyLoad);
      window.addEventListener("resize", lazyLoad);
      window.addEventListener("orientationchange", lazyLoad);
      lazyLoad();
    }
  });
</script>
{% endblock %}

{%- block footer %}
{{ super() }}
<!-- 添加页面加载时间计算 -->
<script>
  document.addEventListener("DOMContentLoaded", function() {
    if (window.performance) {
      const perfData = window.performance.timing;
      const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
      
      const footer = document.querySelector('footer');
      if (footer) {
        const loadTimeInfo = document.createElement('div');
        loadTimeInfo.className = 'page-load-time';
        loadTimeInfo.innerHTML = `页面加载时间: ${pageLoadTime}ms`;
        loadTimeInfo.style.fontSize = '12px';
        loadTimeInfo.style.color = '#999';
        loadTimeInfo.style.marginTop = '5px';
        loadTimeInfo.style.textAlign = 'center';
        
        footer.appendChild(loadTimeInfo);
      }
    }
  });
</script>
{% endblock %}
