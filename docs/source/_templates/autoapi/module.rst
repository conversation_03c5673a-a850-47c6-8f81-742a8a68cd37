{% if not obj.display %}
:orphan:

{% endif %}
{% block title %}

{{ obj.name }} 模块
{{ "=" * (obj.name|length + 7) }}

{% endblock %}

.. py:module:: {{ obj.name }}

{% block body %}
{% if obj.docstring %}
.. autoapi-nested-parse::

   {{ obj.docstring|prepare_docstring|indent(3) }}

{% endif %}

{% block subpackages %}
{% if obj.subpackages %}

子包
-----------

.. toctree::
   :titlesonly:
   :maxdepth: 3

{% for subpackage in obj.subpackages %}
   {{ subpackage.short_name }}/index.rst
{% endfor %}


{% endif %}
{% endblock %}

{% block submodules %}
{% if obj.submodules %}

子模块
----------

.. toctree::
   :titlesonly:
   :maxdepth: 1

{% for submodule in obj.submodules %}
   {{ submodule.short_name }}.rst
{% endfor %}


{% endif %}
{% endblock %}

{% block content %}
{% if obj.children|selectattr("display")|list|length > 0 %}
## 模块内容

{% for obj_item in obj.children|selectattr("display")|sort(attribute="name") %}
{% set fullname = obj_item.id %}
{% set name = obj_item.name %}
{% set item_type = obj_item.type %}
{% set click_to_source = True %}

{% if not obj_item.display %}
:orphan:
{% endif %}

.. py:module:: {{ fullname }}

{% if item_type == 'class' %}
.. py:class:: {{ name }}{% if obj_item.args %}({{ obj_item.args }}){% endif %}
   {% if click_to_source %}:noindex:{% endif %}

   {% if obj_item.bases %}
   {% if obj_item.bases|length == 1 %}
   **继承自**: :class:`{{ obj_item.bases[0] }}`
   {% else %}
   **继承自**:
      {% for base in obj_item.bases %}
      * :class:`{{ base }}`
      {% endfor %}
   {% endif %}
   {% endif %}

   {% if obj_item.docstring %}
   {{ obj_item.docstring|indent(3) }}
   {% endif %}
   
   {% set methods = [] %}
   {% for member_name, member in obj_item.members|dictsort %}
   {% if member.inherited_from is none and member.type == 'method' and not member.name.startswith('_') %}
   {% do methods.append(member) %}
   {% endif %}
   {% endfor %}
   
   {% if methods|length > 0 %}
   **方法**:
   
   {% for member in methods %}
   .. py:method:: {{ member.short_name }}{% if member.args %}({{ member.args }}){% endif %}
      {% if click_to_source %}:noindex:{% endif %}
   
      {% if member.docstring %}
      {{ member.docstring|indent(6) }}
      {% endif %}
   
   {% endfor %}
   {% endif %}
   
   {% set attributes = [] %}
   {% for member_name, member in obj_item.members|dictsort %}
   {% if member.inherited_from is none and member.type == 'attribute' and not member.name.startswith('_') %}
   {% do attributes.append(member) %}
   {% endif %}
   {% endfor %}
   
   {% if attributes|length > 0 %}
   **属性**:
   
   {% for member in attributes %}
   .. py:attribute:: {{ member.short_name }}
      {% if click_to_source %}:noindex:{% endif %}
   
      {% if member.docstring %}
      {{ member.docstring|indent(6) }}
      {% endif %}
   
   {% endfor %}
   {% endif %}

{% elif item_type == 'function' %}
.. py:function:: {{ name }}{% if obj_item.args %}({{ obj_item.args }}){% endif %}
   {% if click_to_source %}:noindex:{% endif %}
   
   {% if obj_item.docstring %}
   {{ obj_item.docstring|indent(3) }}
   {% endif %}

{% else %}
.. py:{{ item_type }}:: {{ name }}
   {% if click_to_source %}:noindex:{% endif %}

   {% if obj_item.docstring %}
   {{ obj_item.docstring|indent(3) }}
   {% endif %}
{% endif %}

{% endfor %}
{% endif %}
{% endblock %}

{% endblock %}
