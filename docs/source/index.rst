=========================
xROCS API Documentation
=========================

.. raw:: html

   <div class="hero-banner">
     <div class="hero-content">
       <h1>xROCS</h1>
       <p class="hero-subtitle">Unified Robot Control System</p>
       <div class="hero-buttons">
         <a href="quick_start.html" class="hero-button primary">Quick Start</a>
         <a href="autoapi/index.html" class="hero-button secondary">API Reference</a>
       </div>
     </div>
   </div>

**xROCS** is a powerful robot control system that provides rich API interfaces for various robotic platforms. This documentation offers a detailed reference guide for the xROCS project, including complete descriptions of all modules, classes, and functions.

Documentation Navigation
==========================

.. raw:: html

   <div class="doc-sections">

.. toctree::
   :maxdepth: 1
   :caption: Getting Started
   :name: start-docs
   :class: start-docs

   introduction
   installation
   quick_start

.. toctree::
   :maxdepth: 1
   :caption: Core Concepts
   :name: core-docs
   :class: core-docs

   architecture
   core_components
   configuration

.. toctree::
   :maxdepth: 1
   :caption: Tutorials and Examples
   :name: tutorial-docs
   :class: tutorial-docs

   tutorials/index
   examples/index

.. toctree::
   :maxdepth: 1
   :caption: API Reference
   :name: api-docs
   :class: api-docs

   autoapi/index

.. toctree::
   :maxdepth: 1
   :caption: Development Guide
   :name: dev-docs
   :class: dev-docs

   contributing
   changelog
