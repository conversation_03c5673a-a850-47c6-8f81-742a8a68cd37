/**
 * xRocs文档自定义JavaScript
 * 增强用户体验和交互性
 */

// 文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 添加回到顶部按钮
    addBackToTopButton();
    
    // 增强代码块的可读性
    enhanceCodeBlocks();
    
    // 添加内容折叠功能
    addContentCollapse();
    
    // 夜间模式已移除
    
    // 添加目录展开/折叠功能
    enhanceTOC();
    
    // 增强移动端体验
    enhanceMobileExperience();
});

/**
 * 添加回到顶部按钮
 */
function addBackToTopButton() {
    // 创建按钮元素
    var backToTopButton = document.createElement('button');
    backToTopButton.innerHTML = '↑';
    backToTopButton.className = 'back-to-top';
    backToTopButton.title = '回到顶部';
    document.body.appendChild(backToTopButton);
    
    // 控制按钮的显示/隐藏
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('show');
        } else {
            backToTopButton.classList.remove('show');
        }
    });
    
    // 点击事件 - 平滑滚动到顶部
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // 添加样式
    var style = document.createElement('style');
    style.innerHTML = `
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #1a73e8;
            color: white;
            font-size: 24px;
            border: none;
            cursor: pointer;
            display: none;
            opacity: 0.7;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .back-to-top:hover {
            opacity: 1;
            transform: translateY(-3px);
        }
        
        .back-to-top.show {
            display: block;
        }
        
        @media (max-width: 768px) {
            .back-to-top {
                width: 40px;
                height: 40px;
                font-size: 20px;
                bottom: 20px;
                right: 20px;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 增强代码块的可读性
 */
function enhanceCodeBlocks() {
    // 获取所有代码块
    var codeBlocks = document.querySelectorAll('div[class^="highlight-"]');
    
    // 为每个代码块添加行号
    codeBlocks.forEach(function(block) {
        var code = block.querySelector('pre');
        if (code) {
            var lines = code.innerHTML.split('\n');
            var numberedLines = '';
            
            for (var i = 0; i < lines.length - 1; i++) {
                numberedLines += '<span class="line-number">' + (i + 1) + '</span>' + lines[i] + '\n';
            }
            
            // 更新代码块内容
            code.innerHTML = numberedLines;
            
            // 添加语言标签
            var languageClass = Array.from(block.classList).find(function(cls) {
                return cls.startsWith('highlight-');
            });
            
            if (languageClass) {
                var language = languageClass.replace('highlight-', '');
                var langBadge = document.createElement('div');
                langBadge.className = 'language-badge';
                langBadge.textContent = language;
                block.insertBefore(langBadge, block.firstChild);
            }
        }
    });
    
    // 添加样式
    var style = document.createElement('style');
    style.innerHTML = `
        div[class^="highlight-"] {
            position: relative;
            margin: 1.5em 0;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .language-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: rgba(26, 115, 232, 0.8);
            color: white;
            padding: 2px 8px;
            font-size: 12px;
            border-radius: 0 6px 0 6px;
            z-index: 1;
        }
        
        .line-number {
            display: inline-block;
            width: 30px;
            margin-right: 10px;
            color: #999;
            text-align: right;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 添加内容折叠功能
 */
function addContentCollapse() {
    // 获取所有的标题
    var headings = document.querySelectorAll('.rst-content h2, .rst-content h3');
    
    headings.forEach(function(heading) {
        // 创建折叠按钮
        var toggleButton = document.createElement('span');
        toggleButton.className = 'collapse-toggle';
        toggleButton.innerHTML = '▾';
        toggleButton.title = '点击折叠/展开';
        heading.insertBefore(toggleButton, heading.firstChild);
        
        // 找到下一个相同级别或更高级别的标题之前的所有内容
        var content = [];
        var next = heading.nextElementSibling;
        while (next && !next.matches('h1, h2' + (heading.tagName === 'H2' ? '' : ', h3'))) {
            content.push(next);
            next = next.nextElementSibling;
        }
        
        // 点击事件处理
        toggleButton.addEventListener('click', function(e) {
            e.stopPropagation();
            
            // 切换状态
            var isCollapsed = toggleButton.classList.toggle('collapsed');
            toggleButton.innerHTML = isCollapsed ? '▸' : '▾';
            
            // 切换内容的可见性
            content.forEach(function(el) {
                el.style.display = isCollapsed ? 'none' : '';
            });
        });
    });
    
    // 添加样式
    var style = document.createElement('style');
    style.innerHTML = `
        .collapse-toggle {
            display: inline-block;
            margin-right: 8px;
            cursor: pointer;
            color: #1a73e8;
            font-size: 14px;
            transition: transform 0.2s;
            width: 16px;
            text-align: center;
        }
        
        .collapse-toggle.collapsed {
            transform: rotate(-90deg);
        }
    `;
    document.head.appendChild(style);
}

/* 夜间模式功能已移除 */

/* 夜间模式功能已移除 */

/**
 * 添加目录展开/折叠功能
 */
function enhanceTOC() {
    var toc = document.querySelector('.wy-menu-vertical');
    if (toc) {
        // 在每个折叠菜单上添加一个图标
        var items = toc.querySelectorAll('li.toctree-l1.current');
        items.forEach(function(item) {
            var submenu = item.querySelector('ul');
            if (submenu) {
                var link = item.querySelector('a');
                if (link) {
                    var toggle = document.createElement('span');
                    toggle.className = 'toc-toggle';
                    toggle.textContent = '▾';
                    link.insertBefore(toggle, link.firstChild);
                    
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        var isExpanded = submenu.style.display !== 'none';
                        submenu.style.display = isExpanded ? 'none' : 'block';
                        toggle.textContent = isExpanded ? '▸' : '▾';
                    });
                }
            }
        });
        
        // 添加样式
        var style = document.createElement('style');
        style.innerHTML = `
            .toc-toggle {
                display: inline-block;
                margin-right: 5px;
                cursor: pointer;
                width: 16px;
                text-align: center;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 增强移动端体验
 */
function enhanceMobileExperience() {
    // 添加移动端菜单切换按钮
    if (window.innerWidth <= 768) {
        var navButton = document.createElement('button');
        navButton.className = 'mobile-nav-toggle';
        navButton.innerHTML = '☰';
        navButton.title = '切换导航菜单';
        document.body.appendChild(navButton);
        
        navButton.addEventListener('click', function() {
            document.body.classList.toggle('nav-open');
        });
        
        // 添加样式
        var style = document.createElement('style');
        style.innerHTML = `
            @media (max-width: 768px) {
                .wy-nav-side {
                    left: -300px;
                    transition: left 0.3s ease;
                }
                
                .nav-open .wy-nav-side {
                    left: 0;
                }
                
                .mobile-nav-toggle {
                    position: fixed;
                    bottom: 30px;
                    left: 20px;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background-color: #1a73e8;
                    color: white;
                    font-size: 20px;
                    border: none;
                    cursor: pointer;
                    z-index: 1001;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                }
                
                .wy-nav-content-wrap {
                    margin-left: 0;
                }
                
                .nav-open .wy-nav-content-wrap::before {
                    content: '';
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-color: rgba(0,0,0,0.5);
                    z-index: 999;
                }
            }
        `;
        document.head.appendChild(style);
    }
}
