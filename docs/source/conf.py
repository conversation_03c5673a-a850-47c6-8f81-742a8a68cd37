# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

import os
import sys

# Add the project root directory to the path so sphinx can find the packages
sys.path.insert(0, os.path.abspath('../..'))

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'xRocs'
copyright = '2025, EAI'
author = 'EAI'
release = '1.6'
language = 'en'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',           # Core Sphinx extension for API docs
    'sphinx.ext.autosummary',       # Generate summaries automatically
    'sphinx.ext.todo',              # Support for todo items
    'sphinx.ext.mathjax',           # Math support
    'sphinx.ext.napoleon',          # Google-style docstring support
    'sphinx.ext.intersphinx',       # Link to other project's documentation
    'sphinx.ext.coverage',          # Check documentation coverage
    'sphinx.ext.inheritance_diagram', # Create inheritance diagrams
    'autoapi.extension',            # Auto API documentation
    'sphinx_copybutton',            # Add copy button to code blocks
    'sphinx.ext.viewcode',          # Add links to view source code
]

# -- Napoleon settings (for Google/NumPy style docstrings) ----------------
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = True
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = True
napoleon_use_admonition_for_notes = True
napoleon_use_admonition_for_references = True
napoleon_use_ivar = True
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = True
napoleon_attr_annotations = True

# -- AutoAPI configuration --------------------------------------------------
autoapi_type = 'python'
autoapi_dirs = ['../../xrocs']
autoapi_ignore = [
    "**/dependencies/bodyctrl_msgs/**",
    "**/dependencies/genmsg/**",
    "**/dependencies/genpy/**",
    "**/dependencies/human_arm_ae_testPub/**",
    "**/__pycache__/**",
    "**/*.pyc",
    "**/.git/**",
]
autoapi_keep_files = False
autoapi_add_to_modules = True
autoapi_file_patterns = ['*.py']
autoapi_python_class_content = 'both'  # Include both class and __init__ docstrings
autoapi_member_order = 'groupwise'  # Group by member type
autoapi_options = [
    "members",
    "undoc-members",
    "show-inheritance",
    "show-module-summary",
    "imported-members",
    "special-members",
]
autoapi_template_dir = '_templates/autoapi'

# -- Intersphinx configuration ----------------------------------------------
intersphinx_mapping = {
    'python': ('https://docs.python.org/3', None),
    'numpy': ('https://numpy.org/doc/stable/', None),
    'scipy': ('https://docs.scipy.org/doc/scipy/', None),
    'matplotlib': ('https://matplotlib.org/stable/', None),
}

# -- Todo extension configuration -------------------------------------------
todo_include_todos = True
todo_emit_warnings = True

# -- AutoDoc configuration --------------------------------------------------
autodoc_default_options = {
    'members': True,
    'undoc-members': True,
    'show-inheritance': True,
    'special-members': '__init__',
}
autodoc_typehints = 'description'
autodoc_member_order = 'groupwise'
autodoc_class_signature = 'separated'
autodoc_docstring_signature = True

# -- Inheritance diagram configuration -------------------------------------
inheritance_graph_attrs = {
    'rankdir': 'TB',
    'size': '"6.0, 8.0"',
    'fontsize': 14,
    'ratio': 'compress',
}
inheritance_node_attrs = {
    'shape': 'rect',
    'fontsize': 14,
    'height': 0.75,
    'color': 'dodgerblue1',
    'style': 'filled',
    'fillcolor': 'lightblue',
}

# Template configuration
templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'sphinx_rtd_theme'
html_theme_options = {
    # Navigation options
    "navigation_depth": 4,
    "collapse_navigation": False,
    "sticky_navigation": True,
    "titles_only": False,
    
    # Display options
    "logo_only": False,
    "prev_next_buttons_location": "both",
    
    # Style options
    "style_nav_header_background": "#1a73e8",
}

# HTML static files
html_static_path = ['_static']
html_css_files = [
    'css/custom.css',
    'css/index.css',
]

# 添加网站图标
html_favicon = '_static/favicon.ico'

# 添加自定义 JavaScript
html_js_files = [
    'js/custom.js',
]

# HTML context
html_context = {
    'display_github': True,
    'github_user': 'EAI',
    'github_repo': 'xRocs',
}

# Other HTML options
html_show_sourcelink = True
html_show_sphinx = True
html_show_copyright = True
html_use_smartypants = True
html_use_index = True
html_domain_indices = True
html_split_index = False
html_copy_source = True
html_short_title = f"{project} {release}"
html_title = f"{project} {release} Documentation"
html_last_updated_fmt = '%Y-%m-%d'
