=========
Examples
=========

This section contains examples of how to use the xROCS API.


xROCS on Tien Yi 2.0
======================

This section contains examples of how to use the xROCS API on Tien Yi 2.0.

MoveIt!-based robot control Examples
--------------------------------------

.. code-block:: python

    from xrocs.entity.station.XTY2.x_tienyi_2 import XTY2Arm
    import time

    robot_dict, camera_dict, hand_dict = {"robot": "127.0.0.1"}, {}, {}
    robot = XTY2Arm(robot_dict, camera_dict, hand_dict)
    robot_arm = robot.get_robot_handle()["robot"]
    robot_arm.connect()


Common Query Examples
^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Query current joints
    current_joints = robot_arm.get_current_joints()

    # Query current left TCP
    current_left_tcp = robot_arm.x_humanoid_left_tcp_pose()

    # Query current right TCP
    current_right_tcp = robot_arm.x_humanoid_right_tcp_pose()


Cartesian Path Planning Examples
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python
    
    # Enable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=True)

    # Add custom obstacles
    robot_arm.x_humanoid_waypoints_add_custom_obstacles(
        obstacles=[
            {"sharp_type": "BOX", "object_name": "box1", "dimensions": [0.2, 0.2, 0.2], "pose": [1.0, 0, 1.0, 0, 0, 0, 1]},
            {"sharp_type": "BOX", "object_name": "box2", "dimensions": [0.2, 0.2, 0.2], "pose": [2.0, 0, 2.0, 0, 0, 0, 1]},
            {"sharp_type": "BOX", "object_name": "box3", "dimensions": [0.2, 0.2, 0.2], "pose": [3.0, 0, 3.0, 0, 0, 0, 1]},
            {"sharp_type": "SPHERE", "object_name": "sphere1", "dimensions": [0.15], "pose": [2.0, 0, 1.0, 0, 0, 0, 1]},
            {"sharp_type": "SPHERE", "object_name": "sphere2", "dimensions": [0.15], "pose": [3.0, 0, 1.0, 0, 0, 0, 1]}]
    )
    # Remove some obstacles
    robot_arm.x_humanoid_waypoints_remove_custom_obstacles(names=["box1", "sphere1"])
    # Remove all obstacles
    robot_arm.x_humanoid_waypoints_remove_all_obstacles()

    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='waist_yaw_link',
        use_cartesian_path=False,
        left_motion_id=10,
        right_motion_id=10,
        left_waypoints=[
            [0.319, 0.24303, 0.064, 0.030847,-0.70643, -0.030894, 0.70644],
            [0.3514, 0.24357, 0.17982, 0.030847,-0.70643, -0.030894, 0.70644]],
        right_waypoints=[
            [0.319, -0.24303, 0.064, -0.030847,-0.70643, 0.030894, 0.70644],
            [0.3514, -0.24357, 0.17982, -0.030847,-0.70643, 0.030894, 0.70644]]
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose only for left arm (TCP)
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='left_tcp_link',
        use_cartesian_path=True,
        left_motion_id=11,
        left_waypoints=[
            [0.05, 0, 0, 0, 0, 0, 1],
            [0.05, 0, 0.05, 0, 0, 0, 1],
            [0.05, 0, -0.1, 0, 0, 0, 1],
            [0.0, 0.1, 0, 0, 0, 0, 1],
            [0.0, -0.1, 0, 0, 0, 0, 1]]
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Reach Cartesian Pose for both left and right arm (TCP) in async mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='left_tcp_link',
        use_cartesian_path=True,
        left_motion_id=12,
        left_waypoints=[
            [0.05, 0, 0, 0, 0, 0, 1],
            [0.05, 0, -0.05, 0, 0, 0, 1]]
    )
    time.sleep(1.0)
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='right_tcp_link',
        use_cartesian_path=True,
        right_motion_id=13,
        right_waypoints=[
            [0.05, 0, 0, 0, 0, 0, 1],
            [0.05, 0, -0.05, 0, 0, 0, 1]]
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Disable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=False)


Joint Space Planning Examples
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Enable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=True)

    # Reach target joints for only left arm (even the right_target_joints is given)
    robot_arm.x_humanoid_reach_joints(
        left_target_joints = [-0.52333, 0.52333, 0.52333, -0.52333, -0.52333, -0.52333, -0.52333],
        right_target_joints = [-0.52333, -0.52333, -0.52333, -0.52333, 0.52333, -0.52333, -0.52333],
        motion_mode = 'left_arm'
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

    # Reach target joints for only right arm (even the left_target_joints is given)
    robot_arm.x_humanoid_reach_joints(
        left_target_joints = [-0.62333, 0.52333, 0.52333, -0.52333, -0.52333, -0.52333, -0.52333],
        right_target_joints = [-0.62333, -0.52333, -0.52333, -0.52333, 0.52333, -0.52333, -0.52333],
        motion_mode = 'right_arm'
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

    # Reach target joints for both left and right arm in sync mode
    robot_arm.x_humanoid_reach_joints(
        left_target_joints = [0., 0., 0., 0., 0., 0., 0.],
        right_target_joints = [0., 0., 0., 0., 0., 0., 0.],
        motion_mode = 'dual_arm'
    )
    # Waiting for Joints Control Action to Finish...
    robot_arm.x_humanoid_wait_joints_control_action_finish()

    # Disable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=False)


Surrounding Obstacle Perception and Planning Examples
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Enable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=True)

    # Enable and Disable Surrounding Obstacle Perception to check the effect
    robot_arm.x_humanoid_enable_surrounding_obstacle_perception()
    robot_arm.x_humanoid_disable_surrounding_obstacle_perception()

    # Enable Surrounding Obstacle Perception
    robot_arm.x_humanoid_enable_surrounding_obstacle_perception()
    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='waist_yaw_link',
        use_cartesian_path=True,
        left_motion_id=10,
        right_motion_id=10,
        left_waypoints=[
            [0.319, 0.24303, 0.064, 0.030847,-0.70643, -0.030894, 0.70644],
            [0.3514, 0.24357, 0.17982, 0.030847,-0.70643, -0.030894, 0.70644]],
        right_waypoints=[
            [0.319, -0.24303, 0.064, -0.030847,-0.70643, 0.030894, 0.70644],
            [0.3514, -0.24357, 0.17982, -0.030847,-0.70643, 0.030894, 0.70644]]
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()


    # Disable Surrounding Obstacle Perception
    robot_arm.x_humanoid_disable_surrounding_obstacle_perception()
    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_reach_cartesian_pose(
        frame='waist_yaw_link',
        use_cartesian_path=True,
        left_motion_id=11,
        right_motion_id=11,
        left_waypoints=[
            [0.319, 0.24303, 0.064, 0.030847,-0.70643, -0.030894, 0.70644],
            [0.3514, 0.24357, 0.17982, 0.030847,-0.70643, -0.030894, 0.70644]],
        right_waypoints=[
            [0.319, -0.24303, 0.064, -0.030847,-0.70643, 0.030894, 0.70644],
            [0.3514, -0.24357, 0.17982, -0.030847,-0.70643, 0.030894, 0.70644]]
    )
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_wait_waypoints_action_finish()

    # Disable MoveIt!
    robot_arm.x_humanoid_set_service_mode(mode="moveit", flag=False)


QP-based robot control Examples
---------------------------------

Cartesian Path Planning Examples
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Enable QP
    robot_arm.x_humanoid_set_service_mode(mode="qp", flag=True)

    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.25, 0.8, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[])
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Update mode
    robot_arm.x_humanoid_sync_update_mode(0)
    # Set time limit
    robot_arm.x_humanoid_sync_set_time_limit(1.0)
    # Set speed limit
    robot_arm.x_humanoid_sync_set_speed_limit(1.5)

    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[])
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Set time limit to default
    robot_arm.x_humanoid_sync_set_time_limit()
    
    # Reach cartesian pose only for left arm (TCP)
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[])
    # Waiting for waypoints action to finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Update mode
    robot_arm.x_humanoid_sync_update_mode(3)

    # Reach cartesian pose step-by-step
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.25, 0.8, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[])
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.5, 1.2, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[])

    # Reach Cartesian Pose for both left and right arm (TCP) in sync mode
    robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.5, 1.0, 0.306, -0.7188, 0.0781, 0.61911]],
                                                   right_waypoints=[[0.45, -0.45, 1.0, -0.3188, -0.7108, -0.06291, 0.623711]])
    # Waiting for Waypoints Action to Finish...
    robot_arm.x_humanoid_sync_reach_cartesian_pose_wait_until_stop()

    # Send cartesian pose for only left arm at high frequency
    for i in range(100):
        robot_arm.x_humanoid_sync_reach_cartesian_pose(left_waypoints=[[0.55, 0.5, 1.0-0.002*i, 0.306, -0.7188, 0.0781, 0.61911]],
                                                       right_waypoints=[])
        time.sleep(0.5)  # You can try different frequency

    # Disable QP
    robot_arm.x_humanoid_set_service_mode(mode="qp", flag=False)


Joint Space Planning Examples
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

    # Enable QP
    robot_arm.x_humanoid_set_service_mode(mode="qp", flag=True)
    
    # Move to new joint position
    target_joints = [0.0, 0.0, 0.0, -2.3968e-05, -0.7192, 0.5602, 0.2825, -0.8836, -0.004841, 0.005659, -0.007409, -0.7173, -0.5712, -0.2500, -0.8837, 0.004913, -0.002226, -0.008403]
    target_joints[3] -= 0.6
    robot_arm.x_humanoid_sync_joints(target_joint=target_joints)

    # Disable QP
    robot_arm.x_humanoid_set_service_mode(mode="qp", flag=False)
