=====================
System Architecture
=====================

xROCS System Architecture Overview
==================

xROCS adopts a modular layered architecture design, ensuring the system's flexibility, scalability, and maintainability.

Main Components
=================

Core Layer
------------
The Core Layer provides the system's foundation architecture and primary functionality.

Entity Layer
--------------

The Entity Layer defines various objects and components in the system:

* **Camera Entities** - Representing camera devices and their components
* **Hand Entities** - Representing hand devices and their components
* **Robot Entities** - Representing physical robots and their components

Application Layer (Apps)
--------------------------

The Application Layer contains user-created applications and examples.

Utilities and Support Modules (Utils & Common)
------------------------------------------------

Provides common functionalities and auxiliary tools

System Interaction Flow
=========================

Coming soon.
