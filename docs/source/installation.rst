====================
Installation Guide
====================

This guide will help you install the xROCS system.

System Requirements
=====================

xROCS requires the following environment:

* Python 3.8+
* Linux os (Ubuntu 22.04 or higher recommended)

Installation Steps
====================

Install from whl file
-----------------------

1. Download the whl file:

   .. code-block:: bash

      wget <Link-to-whl-file>/xRocs-1.6-py3-none-any.whl

2. Install the whl file:

   .. code-block:: bash

      pip install xRocs-1.6-py3-none-any.whl
