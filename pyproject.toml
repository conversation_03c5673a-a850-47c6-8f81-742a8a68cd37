[project]
name = "xrocs"
version = "1.6"
description = "xrocs"
authors = [
    { name = "<PERSON>", email = "<PERSON><PERSON>@x-humanoid.com" }
]
#readme = "README.md"
requires-python = ">= 3.8"
#license = { file = "LICENSE" }
keywords = [
    'artificial intelligence',
    'robotic system',
]

classifiers=[
    'Development Status :: 4 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.9',
]

dependencies = [
    "tqdm",
    "typing_extensions",
    "transforms3d",
    "psutil",
]

[project.urls]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["xrocs"]
