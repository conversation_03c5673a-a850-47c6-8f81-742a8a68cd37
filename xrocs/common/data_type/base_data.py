from __future__ import annotations

from abc import ABC, abstractmethod
import array
import enum
import math
from typing import Generic, List, Literal, Optional, Tuple, Type, TypeVar, Union, overload

import numpy as np
import numpy.typing as npt
from typing_extensions import Self

_TPyData = TypeVar('_TPyData', float, int)
_TNpData = TypeVar('_TNpData', np.float64, np.int32, np.uint8)
"""
np.float64,   array.array[float],     array.array('f', ...),   Float32MultiArray
np.int32,     array.array[int],       array.array('h', ...),   Int16MultiArray
np.uint8,     array.array[int],       array.array('B', ...),   UInt8MultiArray
"""


class BaseData(ABC, Generic[_TPyData, _TNpData]):

    class LengthUnitType(enum.Enum):
        MILLIMETER = 0
        CENTIMETER = 1
        METER = 2

    class AngleUnitType(enum.Enum):
        RADIAN = 0
        DEGREE = 1

    @abstractmethod
    def __repr__(self) -> str:
        ...

    @staticmethod
    def _convert_array_data_length_unit(meter_data: Optional[array.array[float]],
                                        centimeter_data: Optional[array.array[float]],
                                        millimeter_data: Optional[array.array[float]],
                                        to_unit_type: LengthUnitType) -> array.array[float]:
        if meter_data is None and centimeter_data is None and millimeter_data is None:
            raise ValueError

        if to_unit_type == BaseData.LengthUnitType.METER:
            if meter_data is None:
                output_meter_array = array.array('f', [])
                if centimeter_data is not None:
                    for cm in centimeter_data:
                        output_meter_array.append(cm / 100.)
                elif millimeter_data is not None:
                    for mm in millimeter_data:
                        output_meter_array.append(mm / 1000.)
                else:
                    raise ValueError
                return output_meter_array
            else:
                return meter_data
        elif to_unit_type == BaseData.LengthUnitType.CENTIMETER:
            if centimeter_data is None:
                output_centimeter_array = array.array('f', [])
                if meter_data is not None:
                    for m in meter_data:
                        output_centimeter_array.append(m * 100.)
                elif millimeter_data is not None:
                    for mm in millimeter_data:
                        output_centimeter_array.append(mm / 10.)
                else:
                    raise ValueError
                return output_centimeter_array
            else:
                return centimeter_data
        elif to_unit_type == BaseData.LengthUnitType.MILLIMETER:
            if millimeter_data is None:
                output_millimeter_array = array.array('f', [])
                if meter_data is not None:
                    for m in meter_data:
                        output_millimeter_array.append(m * 1000.)
                elif centimeter_data is not None:
                    for cm in centimeter_data:
                        output_millimeter_array.append(cm * 10.)
                else:
                    raise ValueError
                return output_millimeter_array
            else:
                return millimeter_data
        else:
            raise NotImplementedError

    @staticmethod
    def _convert_single_data_length_unit(meter_data: Optional[float], centimeter_data: Optional[float],
                                         millimeter_data: Optional[float], to_unit_type: LengthUnitType) -> float:
        if meter_data is None and centimeter_data is None and millimeter_data is None:
            raise ValueError

        if to_unit_type == BaseData.LengthUnitType.METER:
            if meter_data is None:
                if centimeter_data is not None:
                    return centimeter_data / 100.
                elif millimeter_data is not None:
                    return millimeter_data / 1000.
                else:
                    raise ValueError
            else:
                return meter_data
        elif to_unit_type == BaseData.LengthUnitType.CENTIMETER:
            if centimeter_data is None:
                if meter_data is not None:
                    return meter_data * 100.
                elif millimeter_data is not None:
                    return millimeter_data / 10.
                else:
                    raise ValueError
            else:
                return centimeter_data
        elif to_unit_type == BaseData.LengthUnitType.MILLIMETER:
            if millimeter_data is None:
                if meter_data is not None:
                    return meter_data * 1000.
                elif centimeter_data is not None:
                    return centimeter_data * 10.
                else:
                    raise ValueError
            else:
                return millimeter_data
        else:
            raise NotImplementedError

    @staticmethod
    def _convert_array_data_angle_unit(radian_data: Optional[array.array[float]],
                                       degree_data: Optional[array.array[float]],
                                       to_unit_type: AngleUnitType) -> array.array[float]:
        if radian_data is None and degree_data is None:
            raise ValueError

        if to_unit_type == BaseData.AngleUnitType.RADIAN:
            if radian_data is None:
                output_radian_data = array.array('f', [])
                if degree_data is not None:
                    for degree in degree_data:
                        output_radian_data.append(math.radians(degree))
                else:
                    raise ValueError
                return output_radian_data
            else:
                return radian_data
        elif to_unit_type == BaseData.AngleUnitType.DEGREE:
            if degree_data is None:
                output_degree_data = array.array('f', [])
                if radian_data is not None:
                    for radian in radian_data:
                        output_degree_data.append(math.degrees(radian))
                else:
                    raise ValueError
                return output_degree_data
            else:
                return degree_data
        else:
            raise NotImplementedError

    @staticmethod
    def _convert_single_data_angle_unit(radian_data: Optional[float], degree_data: Optional[float],
                                        to_unit_type: AngleUnitType) -> float:
        if radian_data is None and degree_data is None:
            raise ValueError

        if to_unit_type == BaseData.AngleUnitType.RADIAN:
            if radian_data is None:
                if degree_data is not None:
                    return math.radians(degree_data)
                else:
                    raise ValueError
            else:
                return radian_data
        elif to_unit_type == BaseData.AngleUnitType.DEGREE:
            if degree_data is None:
                if radian_data is not None:
                    return math.degrees(radian_data)
                else:
                    raise ValueError
            else:
                return degree_data
        else:
            raise NotImplementedError


_TSingleData = TypeVar('_TSingleData')  # bound=BaseData


class BaseArray(ABC, Generic[_TPyData, _TNpData, _TSingleData]):

    def __init__(self) -> None:
        self._list: List[_TSingleData] = []
        self._index = -1

    def __repr__(self) -> str:
        return str(self._list)

    def __iter__(self) -> Self:
        self._index = -1
        return self

    def __next__(self) -> _TSingleData:
        self._index += 1
        if self._index < len(self._list):
            return self._list[self._index]
        else:
            raise StopIteration

    def __len__(self) -> int:
        return len(self._list)

    @overload
    def __getitem__(self, key: int) -> _TSingleData:
        ...

    @overload
    def __getitem__(self, key: slice) -> Self:
        ...

    def __getitem__(self, key: Union[int, slice]) -> Union[Self, _TSingleData]:
        """The two overload method above are defined to avoid mypy error prompts.

        i.e. error: Item "PoseArray" of "Union[Pose, PoseArray]" has no attribute "coordinate"
        """
        if isinstance(key, slice):
            new_list = self.__class__()
            for item in self._list[key]:
                new_list.append(item)
            return new_list

        return self._list[key]

    def append(self, data: _TSingleData) -> Self:
        """Return self appended with the provided data.

        Args:
            data: The same data type as _TSingleData.

        Returns:
            The same data type as _TSingleData.
        """
        self._list.append(data)
        return self
