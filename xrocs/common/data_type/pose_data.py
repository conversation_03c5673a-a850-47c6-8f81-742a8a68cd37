from __future__ import annotations

import array
from copy import copy, deepcopy
from typing import Dict, Iterable, Optional, Sequence, Union

import numpy as np
import numpy.typing as npt
import transforms3d.affines
from typing_extensions import Self

from xrocs.common.data_type import BaseArray, BaseData
from xrocs.common.data_type.coordinate_data import Coordinate
from xrocs.common.data_type.rotation_data import Rotation
from xrocs.utils.logger.logger_loader import logger


class Pose(BaseData[float, np.float64]):

    def __init__(self, coordinate: Coordinate, rotation: Rotation) -> None:
        """Create a Pose object, pass a Coordinate and a Rotation object.

        Args:
            coordinate (Coordinate): create with the given coordinate.
            rotation (Rotation): create with the given rotation.
        """
        self._coordinate = deepcopy(coordinate)
        self._rotation = deepcopy(rotation)

    def __repr__(self) -> str:
        return f'Pose: {self._coordinate}; {self._rotation}. '

    def __deepcopy__(self, memo: Optional[Dict[str, int]] = None) -> Pose:
        return Pose(self._coordinate, self._rotation)

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Pose):
            return False
        return self.coordinate == other.coordinate and self.rotation == other.rotation

    def _get_array(self,
                   length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER,
                   rotation_form_type: Rotation.FormType = Rotation.FormType.RPY,
                   angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> array.array[float]:
        assert rotation_form_type != Rotation.FormType.MATRIX, 'The MATRIX rotation is not supported'
        data_array = copy(self._coordinate._get_xyz_array(length_unit_type=length_unit_type))
        data_array.extend(
            self._rotation._get_form_array(rotation_form_type=rotation_form_type, angle_unit_type=angle_unit_type))
        return data_array

    def _get_ndarray(
            self,
            length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER,
            rotation_form_type: Rotation.FormType = Rotation.FormType.RPY,
            angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> npt.NDArray[np.float64]:
        return np.array(
            self._get_array(length_unit_type=length_unit_type,
                            rotation_form_type=rotation_form_type,
                            angle_unit_type=angle_unit_type))

    @property
    def coordinate(self) -> Coordinate:
        """Return the coordinate of Pose."""
        return self._coordinate

    @property
    def rotation(self) -> Rotation:
        """Return the rotation of Pose."""
        return self._rotation

    @property
    def system(self) -> Coordinate.CoordinateSystem:
        """Return the coordination system of the coordinate."""
        return self._coordinate.system

    @property
    def x_m(self) -> float:
        """Return the x value of coordinate."""
        return self.coordinate.x_m

    @property
    def y_m(self) -> float:
        """Return the y value of coordinate."""
        return self.coordinate.y_m

    @property
    def z_m(self) -> float:
        """Return the z value of coordinate."""
        return self.coordinate.z_m

    @property
    def x_cm(self) -> float:
        """Return the x value of coordinate."""
        return self.coordinate.x_cm

    @property
    def y_cm(self) -> float:
        """Return the y value of coordinate."""
        return self.coordinate.y_cm

    @property
    def z_cm(self) -> float:
        """Return the z value of coordinate."""
        return self.coordinate.z_cm

    @property
    def x_mm(self) -> float:
        """Return the x value of coordinate."""
        return self.coordinate.x_mm

    @property
    def y_mm(self) -> float:
        """Return the y value of coordinate."""
        return self.coordinate.y_mm

    @property
    def z_mm(self) -> float:
        """Return the z value of coordinate."""
        return self.coordinate.z_mm

    @property
    def r_degree(self) -> float:
        return self.rotation.r_degree

    @property
    def r_radian(self) -> float:
        return self.rotation.r_radian

    @property
    def p_degree(self) -> float:
        return self.rotation.p_degree

    @property
    def p_radian(self) -> float:
        return self.rotation.p_radian

    @property
    def y_degree(self) -> float:
        return self.rotation.y_degree

    @property
    def y_radian(self) -> float:
        return self.rotation.y_radian

    def get_xyz_m_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in meter, rpy in radian.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_cm_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in cm, rpy in radian.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_mm_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in mm, rpy in radian.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_m_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in meter, rpy in degree.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_cm_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in cm, rpy in degree.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_mm_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z , rx, ry, rz], xyz in mm, rpy in degree.

        Returns:
            a new value in [x, y, z , rx, ry, rz].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_m_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [position (x, y, z) , quaternion (x, y, z, w)]. Position in meter.

        Returns:
            a new value in [position (x, y, z) , quaternion (x, y, z, w)].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.XYZW)

    def get_xyz_cm_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [position (x, y, z) , quaternion (x, y, z, w)]. Position in centimeter.

        Returns:
            a new value in [position (x, y, z) , quaternion (x, y, z, w)].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.XYZW)

    def get_xyz_mm_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [position (x, y, z) , quaternion (x, y, z, w)]. Position in millimeter.

        Returns:
            a new value in [position (x, y, z) , quaternion (x, y, z, w)].
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.XYZW)

    def get_affine_matrix_m_ndarray(self) -> npt.NDArray[np.float64]:
        coordinate_vector = self.coordinate.get_xyz_m_ndarray()
        rotation_matrix = self.rotation.get_matrix_ndarray()
        return np.asarray(transforms3d.affines.compose(coordinate_vector, rotation_matrix, np.ones((3, ))))

    def translate(self, coordinate: Coordinate) -> Pose:
        _coordinate = self._coordinate.translate(coordinate)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_x_m(self, x: float) -> Pose:
        _coordinate = self._coordinate.translate_x_m(x)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_y_m(self, y: float) -> Pose:
        _coordinate = self._coordinate.translate_y_m(y)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_z_m(self, z: float) -> Pose:
        _coordinate = self._coordinate.translate_z_m(z)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_x_cm(self, x: float) -> Pose:
        _coordinate = self._coordinate.translate_x_cm(x)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_y_cm(self, y: float) -> Pose:
        _coordinate = self._coordinate.translate_y_cm(y)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def translate_z_cm(self, z: float) -> Pose:
        _coordinate = self._coordinate.translate_z_cm(z)
        return Pose(coordinate=_coordinate, rotation=self._rotation)

    def rotate_at_self(self, rotation: Rotation) -> Pose:
        _rotation = self._rotation.rotate_at_self(rotation)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_x_radian(self, radian: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_x_radian(radian)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_x_degree(self, degree: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_x_degree(degree)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_y_radian(self, radian: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_y_radian(radian)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_y_degree(self, degree: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_y_degree(degree)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_z_radian(self, radian: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_z_radian(radian)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def rotate_at_self_z_degree(self, degree: float) -> Pose:
        _rotation = self._rotation.rotate_at_self_z_degree(degree)
        return Pose(coordinate=self._coordinate, rotation=_rotation)

    def distance_m(self, other: Union[Coordinate, Pose]) -> float:
        # The coordinate systems are checked in the following Coordinate.distance_m()
        return self.coordinate.distance_m(other)

    def transform_coordinate_system(self, self_origin_to_other_origin: Pose) -> Pose:
        """Return a pose whose coordinate system is transformed by the given input (Pose).

        Given the point x's affine matrix [self = P_A(x)] under coordinate system A,
        and another system B with [self_origin_to_other_origin = P_B(A)]
            - Transform coordinate system (change axis): P_B(x) = P_B(A) @ P_A(x)
              i.e., P_A(x).transform(P_B(A)) get: P_B(x) = P_B(A) @ P_A(x)

        Args:
            self_origin_to_other_origin: P_B(A)

        Returns:
            a new Pose representing the same point in the new coordinate system (same as other).
        """
        # The coordinate systems are checked in the following Coordinate.transform_coordinate_system()
        coordinate = self.coordinate.transform_coordinate_system(self_origin_to_other_origin)
        rotation = self_origin_to_other_origin.rotation.rotate_at_self(self.rotation)
        return Pose(coordinate, rotation)

    @classmethod
    def get_origin_of(cls, coordinate_system_type: Coordinate.CoordinateSystem) -> Self:
        return cls(
            Coordinate(data=[0., 0., 0.], coordinate_system_type=coordinate_system_type),
            Rotation([0., 0., 0.],
                     rotation_form_type=Rotation.FormType.RPY,
                     angle_unit_type=Rotation.AngleUnitType.RADIAN))

    @classmethod
    def from_affine_matrix(cls, data: Union[Iterable[Iterable[float]], npt.NDArray[np.float64]],
                           coordinate_system_type: Coordinate.CoordinateSystem,
                           length_unit_type: BaseData.LengthUnitType) -> Self:
        data = np.asarray(data)
        return cls(
            Coordinate(data[:3, 3], coordinate_system_type=coordinate_system_type, length_unit_type=length_unit_type),
            Rotation(data[:3, :3], rotation_form_type=Rotation.FormType.MATRIX))


class PoseArray(BaseArray[float, np.float64, Pose]):

    def __init__(self,
                 pose_array_data: Optional[Union[npt.NDArray[np.float64], Sequence[Sequence[float]],
                                                 Sequence[Pose]]] = None,
                 coordinate_system_type: Coordinate.CoordinateSystem = Coordinate.CoordinateSystem.ARM_BASE,
                 length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER,
                 rotation_form_type: Rotation.FormType = Rotation.FormType.RPY,
                 angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> None:
        """
        Args:
            pose_array_data: The pose trajectory.
                i.e., "xyzrpy" pose sequence, in the "xyzrpy" format, of shape (n, 6).
                i.e., "xyzxyzw" pose sequence, in the "xyzxyzw" format, of shape (n, 7).
            coordinate_system_type
            length_unit_type
            angle_unit_type
        """
        assert rotation_form_type != Rotation.FormType.MATRIX, 'The MATRIX rotation is not supported'

        super().__init__()

        if pose_array_data is not None:
            if isinstance(pose_array_data, np.ndarray):
                if pose_array_data.shape[0] == 0:
                    return

                if rotation_form_type == Rotation.FormType.RPY:
                    assert pose_array_data.shape[1] == 6, 'The shape is not correct! Expect (n, 6)'
                elif rotation_form_type == Rotation.FormType.XYZW:
                    assert pose_array_data.shape[1] == 7, 'The shape is not correct! Expect (n, 7)'

                for i in range(pose_array_data.shape[0]):
                    new_pose = Pose(coordinate=Coordinate(pose_array_data[i][:3],
                                                          coordinate_system_type=coordinate_system_type,
                                                          length_unit_type=length_unit_type),
                                    rotation=Rotation(pose_array_data[i][3:],
                                                      rotation_form_type=rotation_form_type,
                                                      angle_unit_type=angle_unit_type))
                    self._list.append(new_pose)
            elif isinstance(pose_array_data, Sequence):
                for pose_data in pose_array_data:
                    if isinstance(pose_data, Sequence):
                        new_pose = Pose(coordinate=Coordinate(pose_data[:3],
                                                              coordinate_system_type=coordinate_system_type,
                                                              length_unit_type=length_unit_type),
                                        rotation=Rotation(pose_data[3:],
                                                          rotation_form_type=rotation_form_type,
                                                          angle_unit_type=angle_unit_type))
                        self._list.append(new_pose)
                    elif isinstance(pose_data, Pose):
                        if coordinate_system_type != pose_data.system:
                            logger.warning(f'The input coordinate_system_type={coordinate_system_type} is ignored; '
                                           f'The value {pose_data.system} from Pose is used.')
                        self._list.append(deepcopy(pose_data))
                    else:
                        raise NotImplementedError
            else:
                raise NotImplementedError

    def _get_coordinate_ndarray(
            self,
            length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER) -> npt.NDArray[np.float64]:

        concatenate_list = []
        for pose in self._list:
            concatenate_list.append(pose._coordinate._get_xyz_array(length_unit_type=length_unit_type))
        return np.array(concatenate_list)

    def _get_ndarray(
            self,
            length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER,
            rotation_form_type: Rotation.FormType = Rotation.FormType.RPY,
            angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> npt.NDArray[np.float64]:
        assert rotation_form_type != Rotation.FormType.MATRIX, 'The MATRIX rotation is not supported'

        if len(self._list) == 0:
            return np.zeros(0)

        concatenate_list = []
        rotation_list = []
        for pose in self._list:
            concatenate_list.append(pose._coordinate._get_xyz_array(length_unit_type=length_unit_type))
            rotation_list.append(
                pose._rotation._get_form_array(rotation_form_type=rotation_form_type, angle_unit_type=angle_unit_type))
        return np.concatenate((concatenate_list, rotation_list), axis=1)

    @property
    def system(self) -> Optional[Coordinate.CoordinateSystem]:
        """Return the coordination system of the pose array.

        Return None if no data or >1 systems exist.
        """
        ret = None
        for pose in self._list:
            if ret is None:
                ret = pose.system
            elif ret != pose.system:
                return None
        return ret

    def get_xyz_m_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z]), xyz in meter.

        Returns:
            a new value in (n_steps, [x, y, z]).
        """
        return self._get_coordinate_ndarray(length_unit_type=BaseData.LengthUnitType.METER)

    def get_xyz_cm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z]), xyz in cm.

        Returns:
            a new value in (n_steps, [x, y, z]).
        """
        return self._get_coordinate_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER)

    def get_xyz_mm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z ]), xyz in mm.

        Returns:
            a new value in (n_steps, [x, y, z]).
        """
        return self._get_coordinate_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER)

    def get_xyz_m_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in meter, rpy in radian.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_cm_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in cm, rpy in radian.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_mm_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in mm, rpy in radian.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN)

    def get_xyz_m_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in meter, rpy in degree.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_cm_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in cm, rpy in degree.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_mm_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [x, y, z , rx, ry, rz]), xyz in mm, rpy in degree.

        Returns:
            a new value in (n_steps, [x, y, z , rx, ry, rz]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE)

    def get_xyz_m_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]). Position in meter.

        Returns:
            a new value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER,
                                 rotation_form_type=Rotation.FormType.XYZW)

    def get_xyz_cm_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]). Position in centimeter.

        Returns:
            a new value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                                 rotation_form_type=Rotation.FormType.XYZW)

    def get_xyz_mm_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]). Position in millimeter.

        Returns:
            a new value in (n_steps, [position (x, y, z) , quaternion (x, y, z, w)]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                                 rotation_form_type=Rotation.FormType.XYZW)

