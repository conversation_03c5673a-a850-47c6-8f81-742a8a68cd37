from __future__ import annotations

import array
from copy import deepcopy
from typing import Dict, Iterable, List, Optional, Sequence, Union

import numpy as np
import numpy.typing as npt

from xrocs.common.data_type import BaseArray, BaseData


class Joints(BaseData[float, np.float64]):

    def __init__(self,
                 data: Iterable[float],
                 num_of_dofs: int = 6,
                 angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> None:
        """
        Args:
            data: the data form should be (num_of_dofs,).
            angle_unit_type: the unit type can be BaseData.AngleUnitType.RADIAN or BaseData.AngleUnitType.DEGREE.
        """
        self._radian_data: Optional[array.array[float]] = None
        self._degree_data: Optional[array.array[float]] = None

        if angle_unit_type == BaseData.AngleUnitType.RADIAN:
            self._radian_data = array.array('f', data)
            assert len(self._radian_data) == num_of_dofs, 'shape is incorrect!'
        else:
            self._degree_data = array.array('f', data)
            assert len(self._degree_data) == num_of_dofs, 'shape is incorrect!'

        self._unit_type = angle_unit_type

    def __repr__(self) -> str:
        return (f'Joints: degree={self._degree_data.tolist() if self._degree_data is not None else None}, '
                f'radian={self._radian_data.tolist() if self._radian_data is not None else None}')

    def __deepcopy__(self, memo: Optional[Dict[str, int]] = None) -> Joints:
        if self._radian_data is not None:
            return Joints(self._radian_data, angle_unit_type=BaseData.AngleUnitType.RADIAN)

        if self._degree_data is not None:
            return Joints(self._degree_data, angle_unit_type=BaseData.AngleUnitType.DEGREE)

        raise ValueError

    def _get_array(self,
                   angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> array.array[float]:
        """Get the data in a 1-d array.array.

        Returns:
            of shape (num_of_dofs, )
        """
        data_array = self._convert_array_data_angle_unit(radian_data=self._radian_data,
                                                         degree_data=self._degree_data,
                                                         to_unit_type=angle_unit_type)

        if angle_unit_type == BaseData.AngleUnitType.RADIAN:
            self._radian_data = data_array
            return self._radian_data
        elif angle_unit_type == BaseData.AngleUnitType.DEGREE:
            self._degree_data = data_array
            return self._degree_data
        else:
            raise NotImplementedError

    @property
    def unit(self) -> BaseData.AngleUnitType:
        """Return the unit of the Joints."""
        return self._unit_type

    def get_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in radian.

        Returns:
            a ndarray in radian.
        """
        return np.array(self._get_array(BaseData.AngleUnitType.RADIAN))

    def get_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in degree.

        Returns:
            a ndarray in degree.
        """
        return np.array(self._get_array(BaseData.AngleUnitType.DEGREE))


class JointsArray(BaseArray[float, np.float64, Joints]):

    def __init__(self,
                 data: Optional[Union[npt.NDArray[np.float64], Sequence[Iterable[float]], Sequence[Joints]]] = None,
                 angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> None:
        """
        Args:
            data: the data form can be (n, 7).
            angle_unit_type: the unit type can be self.UnitType.RADIAN or self.UnitType.DEGREE.
        """
        super().__init__()

        if data is not None:
            if isinstance(data, np.ndarray):
                for i in range(data.shape[0]):
                    self._list.append(Joints(data[i].ravel(), angle_unit_type=angle_unit_type))
            elif isinstance(data, Sequence):
                for joints in data:
                    if isinstance(joints, Iterable):
                        self._list.append(Joints(joints, angle_unit_type=angle_unit_type))
                    elif isinstance(joints, Joints):
                        self._list.append(deepcopy(joints))
                    else:
                        raise NotImplementedError
            else:
                raise NotImplementedError
        self._unit_type = angle_unit_type

    def __repr__(self) -> str:
        degree_array_list = []
        radian_array_list = []
        # 直接访问内部变量，可输出原始Joints数据，避免产生数据副本。如使用get_radian_ndarray()等方法会创造新的数据副本
        for joints in self._list:
            degree_array_list.append(joints._degree_data)
            radian_array_list.append(joints._radian_data)

        return f'Degree: {degree_array_list}, Radian: {radian_array_list}'

    def _get_array_list(self,
                        angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN
                        ) -> List[array.array[float]]:
        array_list = []
        for joints in self._list:
            # 直接访问内部变量，避免产生数据副本。如使用get_radian_ndarray()等方法会创造新的数据副本
            array_list.append(joints._get_array(angle_unit_type=angle_unit_type))

        return array_list

    def get_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return ndarray value in radian.

        Returns:
            a ndarray in radian.
        """
        return np.array(self._get_array_list())

    def get_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return ndarray value in degree.

        Returns:
            a ndarray in degree.
        """
        return np.array(self._get_array_list(angle_unit_type=BaseData.AngleUnitType.DEGREE))
