from __future__ import annotations

import array
from enum import Enum
import math
from typing import Dict, Final, Iterable, Optional, Union

import numpy as np
import numpy.typing as npt
import transforms3d as t3d

from xrocs.common.data_type import BaseData


class Rotation(BaseData[float, np.float64]):

    class FormType(Enum):
        RPY = 0  # raw, pitch, yaw along the x, y, z axis, respectively, 'sxyz' in transforms3d
        XYZW = 1
        MATRIX = 2

    SHAPE: Final = {FormType.RPY: (3, ), FormType.XYZW: (4, ), FormType.MATRIX: (3, 3)}

    def __init__(self,
                 data: Union[Iterable[float], npt.NDArray[np.float64]],
                 rotation_form_type: FormType = FormType.RPY,
                 angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> None:
        """Create a Rotation object.

        Args:
            data: the data can be
                (3, ) for rpy,
                (4, ) for xyzw,
                (3, 3) for matrix.
            rotation_form_type (Rotation.FormType): specific the data form.
            angle_unit_type (BaseData.AngleUnitType): specific the unit of data.
        """
        self._rpy_radian_data: Optional[array.array[float]] = None
        self._rpy_degree_data: Optional[array.array[float]] = None
        self._xyzw_data: Optional[array.array[float]] = None
        self._matrix_data: Optional[npt.NDArray[np.float64]] = None

        if rotation_form_type in (self.FormType.XYZW, self.FormType.MATRIX):
            assert angle_unit_type == BaseData.AngleUnitType.RADIAN, 'WXYZ and MATRIX must be RADIAN!'

        if rotation_form_type == Rotation.FormType.RPY:
            if angle_unit_type == BaseData.AngleUnitType.RADIAN:
                self._rpy_radian_data = array.array('f', data)
                assert len(self._rpy_radian_data) == 3, (
                    f'{rotation_form_type} shape is not correct! '
                    f'Expect {self.SHAPE[rotation_form_type]}; got ({len(self._rpy_radian_data)}, ).')
            else:
                self._rpy_degree_data = array.array('f', data)
                assert len(self._rpy_degree_data) == 3, (
                    f'{rotation_form_type} shape is not correct! '
                    f'Expect {self.SHAPE[rotation_form_type]}; got ({len(self._rpy_degree_data)}, ).')
        elif rotation_form_type == Rotation.FormType.XYZW:
            self._xyzw_data = array.array('f', data)
            assert len(
                self._xyzw_data) == 4, (f'{rotation_form_type} shape is not correct! '
                                        f'Expect {self.SHAPE[rotation_form_type]}; got ({len(self._xyzw_data)}, ).')
        else:  # rotation_form_type == Rotation.Form.MATRIX
            self._matrix_data = np.array(data, dtype=np.float64)
            assert self._matrix_data.shape[:] == self.SHAPE[rotation_form_type], (
                f'{rotation_form_type} shape is not correct! '
                f'Expect {self.SHAPE[rotation_form_type]}; got {self._matrix_data.shape[:]}.')

        self._form_type = rotation_form_type
        self._unit_type = angle_unit_type

    def __repr__(self) -> str:
        return (f'Rotation: '
                f'rpy_radian={self._rpy_radian_data.tolist() if self._rpy_radian_data is not None else None}, '
                f'rpy_degree={self._rpy_degree_data.tolist() if self._rpy_degree_data is not None else None}, '
                f'xyzw={self._xyzw_data.tolist() if self._xyzw_data is not None else None}, '
                f'matrix={self._matrix_data.tolist() if self._matrix_data is not None else None}.')

    def __deepcopy__(self, memo: Optional[Dict[str, int]] = None) -> Rotation:
        if self._rpy_radian_data is not None:
            return Rotation(self._rpy_radian_data,
                            rotation_form_type=Rotation.FormType.RPY,
                            angle_unit_type=BaseData.AngleUnitType.RADIAN)

        if self._rpy_degree_data is not None:
            return Rotation(self._rpy_degree_data,
                            rotation_form_type=Rotation.FormType.RPY,
                            angle_unit_type=BaseData.AngleUnitType.DEGREE)

        if self._xyzw_data is not None:
            return Rotation(self._xyzw_data, rotation_form_type=Rotation.FormType.XYZW)

        if self._matrix_data is not None:
            return Rotation(self._matrix_data, rotation_form_type=Rotation.FormType.MATRIX)

        raise ValueError

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Rotation):
            return False
        if not np.allclose(self.get_xyzw_ndarray(), other.get_xyzw_ndarray(), rtol=1e-5, atol=1e-8):
            return False
        return True

    def _get_form_array(self,
                        *,
                        rotation_form_type: FormType = FormType.RPY,
                        angle_unit_type: BaseData.AngleUnitType = BaseData.AngleUnitType.RADIAN) -> array.array[float]:
        # MATRIX is saved in npt.NDArray, while others are saved in array.array
        assert rotation_form_type != Rotation.FormType.MATRIX, 'The MATRIX rotation is not supported'

        if rotation_form_type == Rotation.FormType.RPY:
            if angle_unit_type == BaseData.AngleUnitType.RADIAN:
                if self._rpy_radian_data is None:
                    if self._rpy_degree_data is not None:
                        self._rpy_radian_data = array.array('f', [])
                        for rpy_degree_data in self._rpy_degree_data:
                            self._rpy_radian_data.append(math.radians(rpy_degree_data))
                    elif self._xyzw_data is not None:
                        wxyz = (self._xyzw_data[3], *self._xyzw_data[0:3])
                        res = t3d.euler.quat2euler(wxyz, axes='sxyz')
                        self._rpy_radian_data = array.array('f', res)
                    elif self._matrix_data is not None:
                        res = t3d.euler.mat2euler(self._matrix_data, axes='sxyz')
                        self._rpy_radian_data = array.array('f', res)
                    else:
                        raise ValueError

                return self._rpy_radian_data
            else:  # angle_unit_type == BaseData.AngleUnitType.DEGREE
                if self._rpy_degree_data is None:
                    # 获取radian_rpy
                    radian_rpy = self._get_form_array(rotation_form_type=Rotation.FormType.RPY,
                                                      angle_unit_type=BaseData.AngleUnitType.RADIAN)

                    self._rpy_degree_data = array.array('f', [])
                    for rpy_radian_data in radian_rpy:
                        self._rpy_degree_data.append(math.degrees(rpy_radian_data))
                return self._rpy_degree_data
        elif rotation_form_type == Rotation.FormType.XYZW:
            if self._xyzw_data is None:
                if self._rpy_radian_data is not None or self._rpy_degree_data is not None:
                    # 获取radian_rpy
                    rpy_radian = self._get_form_array(rotation_form_type=Rotation.FormType.RPY,
                                                      angle_unit_type=BaseData.AngleUnitType.RADIAN)

                    quaternion = t3d.euler.euler2quat(*rpy_radian, axes='sxyz')
                    # from WXYZ to XYZW
                    self._xyzw_data = array.array('f', [*quaternion[1:4], quaternion[0]])
                elif self._matrix_data is not None:
                    quaternion = t3d.quaternions.mat2quat(self._matrix_data)
                    # from WXYZ to XYZW
                    self._xyzw_data = array.array('f', [*quaternion[1:4], quaternion[0]])
                else:
                    raise ValueError

            return self._xyzw_data
        else:
            raise NotImplementedError(f'Unsupported form_type `{rotation_form_type}`.')

    @property
    def r_degree(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.DEGREE)[0]

    @property
    def r_radian(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.RADIAN)[0]

    @property
    def p_degree(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.DEGREE)[1]

    @property
    def p_radian(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.RADIAN)[1]

    @property
    def y_degree(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.DEGREE)[2]

    @property
    def y_radian(self) -> float:
        return self._get_form_array(rotation_form_type=self.FormType.RPY,
                                    angle_unit_type=BaseData.AngleUnitType.RADIAN)[2]

    @property
    def form(self) -> FormType:
        """Get the form type."""
        return self._form_type

    @property
    def unit(self) -> BaseData.AngleUnitType:
        """Get the unit type."""
        return self._unit_type

    def get_matrix_ndarray(self) -> npt.NDArray[np.float64]:
        """Return matrix value, of shape (3, 3).

        Returns:
            a ndarray.
        """
        if self._matrix_data is None:
            if self._rpy_radian_data is not None or self._rpy_degree_data is not None:
                # 获取radian_rpy
                rpy_radian = self._get_form_array(rotation_form_type=Rotation.FormType.RPY,
                                                  angle_unit_type=BaseData.AngleUnitType.RADIAN)
                self._matrix_data = t3d.euler.euler2mat(*rpy_radian, axes='sxyz')
            elif self._xyzw_data is not None:
                # XYZW to WXYZ
                wxyz = (self._xyzw_data[3], *self._xyzw_data[0:3])
                self._matrix_data = t3d.quaternions.quat2mat(wxyz)
            else:  # _matrix_data, _rpy_radian_data, _rpy_degree_data, _xyzw_data are all None
                raise RuntimeError('All data fields are None!')
        return np.array(self._matrix_data)

    def get_rpy_radian_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [rx, ry, rz], rpy in radian.

        Returns:
            a new value in [rx, ry, rz].
        """
        return np.array(
            self._get_form_array(rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.RADIAN))

    def get_rpy_degree_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [rx, ry, rz], rpy in degree.

        Returns:
            a new value in [rx, ry, rz].
        """
        return np.array(
            self._get_form_array(rotation_form_type=Rotation.FormType.RPY,
                                 angle_unit_type=BaseData.AngleUnitType.DEGREE))

    def get_xyzw_ndarray(self) -> npt.NDArray[np.float64]:
        """Return xyzw value.

        Returns:
            a ndarray.
        """
        return np.array(self._get_form_array(rotation_form_type=Rotation.FormType.XYZW))

    def get_wxyz_ndarray(self) -> npt.NDArray[np.float64]:
        """Return xyzw value.

        Returns:
            a ndarray.
        """
        xyzw = self._get_form_array(rotation_form_type=Rotation.FormType.XYZW)
        return np.concatenate([[xyzw[3]], xyzw[:3]])

    def rotate_at_self(self, rotation: Rotation) -> Rotation:
        """Rotate the rotation with another (i.e., target) Rotation object.

        Given the rotation matrix self = R_A under coordinate system A, and rotation = R->R'
            - Rotate point: R'_a = R_a @ (R->R')

        Args:
            rotation (Rotation): a Rotation RPY, R'_a
        """
        result = self.get_matrix_ndarray() @ rotation.get_matrix_ndarray()
        return Rotation(result, rotation_form_type=Rotation.FormType.MATRIX)

    def rotate_at_self_x_radian(self, radian: float) -> Rotation:
        return self.rotate_at_self(Rotation([radian, 0., 0.], angle_unit_type=Rotation.AngleUnitType.RADIAN))

    def rotate_at_self_x_degree(self, degree: float) -> Rotation:
        return self.rotate_at_self(Rotation([degree, 0., 0.], angle_unit_type=Rotation.AngleUnitType.DEGREE))

    def rotate_at_self_y_radian(self, radian: float) -> Rotation:
        return self.rotate_at_self(Rotation([0., radian, 0.], angle_unit_type=Rotation.AngleUnitType.RADIAN))

    def rotate_at_self_y_degree(self, degree: float) -> Rotation:
        return self.rotate_at_self(Rotation([0., degree, 0.], angle_unit_type=Rotation.AngleUnitType.DEGREE))

    def rotate_at_self_z_radian(self, radian: float) -> Rotation:
        return self.rotate_at_self(Rotation([0., 0., radian], angle_unit_type=Rotation.AngleUnitType.RADIAN))

    def rotate_at_self_z_degree(self, degree: float) -> Rotation:
        return self.rotate_at_self(Rotation([0., 0., degree], angle_unit_type=Rotation.AngleUnitType.DEGREE))

