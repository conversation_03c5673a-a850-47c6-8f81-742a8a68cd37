from __future__ import annotations

import array
import math
import typing
from copy import deepcopy
from enum import Enum
from typing import Dict, Iterable, Optional, Sequence, Union

import numpy as np
import numpy.typing as npt

from xrocs.common.data_type import BaseArray, BaseData
from xrocs.utils.logger.logger_loader import logger

if typing.TYPE_CHECKING:
    from xrocs.common.data_type.pose_data import Pose


class Coordinate(BaseData[float, np.float64]):

    class CoordinateSystem(Enum):
        ARM_BASE = 0
        ARM_END_EFFECTOR = 1
        HANDEYE_CAMERA = 2
        HEAD_BASE = 3
        HEAD_CAMERA = 4
        CHASSIS_BASE = 5
        NONE = 999

    def __init__(self,
                 data: Iterable[float],
                 coordinate_system_type: CoordinateSystem = CoordinateSystem.ARM_BASE,
                 length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER) -> None:
        """

        Args:
            data: [x, y, z]
            coordinate_system_type: the coordinate system
            length_unit_type: the unit type can be MILLIMETER or CENTIMETER or METER
        """

        self._mm_data: Optional[array.array[float]] = None
        self._cm_data: Optional[array.array[float]] = None
        self._m_data: Optional[array.array[float]] = None

        if length_unit_type == BaseData.LengthUnitType.METER:
            self._m_data = array.array('f', data)
            assert len(self._m_data) == 3, 'The shape is not correct! Expect (3, )'
        elif length_unit_type == BaseData.LengthUnitType.CENTIMETER:
            self._cm_data = array.array('f', data)
            assert len(self._cm_data) == 3, 'The shape is not correct! Expect (3, )'
        else:
            self._mm_data = array.array('f', data)
            assert len(self._mm_data) == 3, 'The shape is not correct! Expect (3, )'

        self._system_type = coordinate_system_type
        self._unit_type = length_unit_type

    def __repr__(self) -> str:
        return (f'Coordinate: m={self._m_data.tolist() if self._m_data is not None else None}, '
                f'cm={self._cm_data.tolist() if self._cm_data is not None else None}, '
                f'mm={self._mm_data.tolist() if self._mm_data is not None else None}, '
                f'in the {self._system_type} coordinate system')

    def __deepcopy__(self, memo: Optional[Dict[str, int]] = None) -> Coordinate:
        if self._m_data is not None:
            return Coordinate(self._m_data,
                              length_unit_type=BaseData.LengthUnitType.METER,
                              coordinate_system_type=self._system_type)

        if self._cm_data is not None:
            return Coordinate(self._cm_data,
                              length_unit_type=BaseData.LengthUnitType.CENTIMETER,
                              coordinate_system_type=self._system_type)

        if self._mm_data is not None:
            return Coordinate(self._mm_data,
                              length_unit_type=BaseData.LengthUnitType.MILLIMETER,
                              coordinate_system_type=self._system_type)

        raise ValueError

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Coordinate):
            return False
        if self.system != other.system:
            return False
        if not np.allclose(self.get_xyz_m_ndarray(), other.get_xyz_m_ndarray(), rtol=1e-5, atol=1e-8):
            return False
        return True

    def _get_xyz_array(self,
                       length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER
                       ) -> array.array[float]:
        data_array = self._convert_array_data_length_unit(meter_data=self._m_data,
                                                          centimeter_data=self._cm_data,
                                                          millimeter_data=self._mm_data,
                                                          to_unit_type=length_unit_type)
        if length_unit_type == BaseData.LengthUnitType.METER:
            self._m_data = data_array
            return self._m_data
        elif length_unit_type == BaseData.LengthUnitType.CENTIMETER:
            self._cm_data = data_array
            return self._cm_data
        elif length_unit_type == BaseData.LengthUnitType.MILLIMETER:
            self._mm_data = data_array
            return self._mm_data
        else:
            raise NotImplementedError

    @property
    def x_m(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.METER)[0]

    @property
    def y_m(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.METER)[1]

    @property
    def z_m(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.METER)[2]

    @property
    def x_cm(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[0]

    @property
    def y_cm(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[1]

    @property
    def z_cm(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[2]

    @property
    def x_mm(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[0]

    @property
    def y_mm(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[1]

    @property
    def z_mm(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[2]

    @property
    def unit(self) -> BaseData.LengthUnitType:
        """Return the unit of the coordinate."""
        return self._unit_type

    @property
    def system(self) -> CoordinateSystem:
        """Return the coordination system of the coordinate."""
        return self._system_type

    def get_xyz_m_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in meter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.METER))

    def get_xyz_cm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in centimeter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER))

    def get_xyz_mm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in millimeter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER))

    def translate(self, movement: Coordinate) -> Coordinate:
        """Translate the coordinate with a movement.

        Given the coordinate self = T_A(x) under coordinate system A, and movement = T_x(x') = T_A(x->x')
            - Translate point (move): T_A(x') =  T_A(x) + T_x(x')

        Args:
            movement: a translation coordinate.
        Returns:
            a new Coordinate object, T_A(x')
        """
        self_data = self._get_xyz_array(length_unit_type=movement.unit)
        coordinate_data = movement._get_xyz_array(length_unit_type=movement.unit)
        return Coordinate(
            [self_data[0] + coordinate_data[0], self_data[1] + coordinate_data[1], self_data[2] + coordinate_data[2]],
            coordinate_system_type=self._system_type,
            length_unit_type=movement.unit)

    def translate_x_m(self, x: float) -> Coordinate:
        return self.translate(
            Coordinate([x, 0., 0.],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.METER))

    def translate_y_m(self, y: float) -> Coordinate:
        return self.translate(
            Coordinate([0., y, 0.],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.METER))

    def translate_z_m(self, z: float) -> Coordinate:
        return self.translate(
            Coordinate([0., 0., z],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.METER))

    def translate_x_cm(self, x: float) -> Coordinate:
        return self.translate(
            Coordinate([x, 0., 0.],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.CENTIMETER))

    def translate_y_cm(self, y: float) -> Coordinate:
        return self.translate(
            Coordinate([0., y, 0.],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.CENTIMETER))

    def translate_z_cm(self, z: float) -> Coordinate:
        return self.translate(
            Coordinate([0., 0., z],
                       coordinate_system_type=self.system,
                       length_unit_type=Coordinate.LengthUnitType.CENTIMETER))

    def distance_m(self, other: Union[Coordinate, Pose]) -> float:
        if isinstance(other, Coordinate):
            if self.system != other.system:
                raise ValueError('To calculate the distance, the two coordinate systems (axes) should be the same.'
                                 f'self: {self.system}; other: {other.system}.')
            return math.sqrt(np.sum((self.get_xyz_m_ndarray() - other.get_xyz_m_ndarray())**2))
        else:  # isinstance(other, Pose):
            return self.distance_m(other.coordinate)

    def transform_coordinate_system(self, self_origin_to_other_origin: Pose) -> Coordinate:
        """Return a pose whose coordinate system is transformed by the given input (Pose).

        Given the point x's affine matrix [self = P_A(x)] under coordinate system A,
        and another system B with [self_origin_to_other_origin = P_B(A)]
            - Transform coordinate system (change axis): P_B(x) = P_B(A) @ P_A(x)
              i.e., P_A(x).transform(P_B(A)) get: P_B(x) = P_B(A) @ P_A(x)

        Args:
            self_origin_to_other_origin: P_B(A)

        Returns:
            a new Pose representing the same point in the new coordinate system (same as other).
        """
        # Valid: two systems are different, or they are both None.
        if self.system == self_origin_to_other_origin.system:
            if self.system != Coordinate.CoordinateSystem.NONE:
                raise ValueError('The two coordinate systems (axes) are the same: self.system can not be transformed. '
                                 'If you want to move the point within the same coordinate system, '
                                 'use Pose.translate() or Pose.rotate_at_self().')
        coordinate = self_origin_to_other_origin.coordinate.translate(
            Coordinate(self_origin_to_other_origin.rotation.get_matrix_ndarray() @ self.get_xyz_m_ndarray(),
                       length_unit_type=self.LengthUnitType.METER,
                       coordinate_system_type=self.system))
        return coordinate


class CoordinateArray(BaseArray[float, np.float64, Coordinate]):

    def __init__(self,
                 array_data: Optional[Union[npt.NDArray[np.float64], Sequence[Iterable[float]],
                                            Sequence[Coordinate]]] = None,
                 coordinate_system_type: Coordinate.CoordinateSystem = Coordinate.CoordinateSystem.ARM_BASE,
                 length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER) -> None:
        """
        Args:
            array_data: The Coordinate array.the data form can be (n, 3)
            coordinate_system_type: the coordinate system
            length_unit_type: the unit type can be
                self.UnitType.MILLIMETER or self.UnitType.CENTIMETER or
                self.UnitType.METER
        """

        super().__init__()

        if array_data is not None:
            if isinstance(array_data, np.ndarray):
                for i in range(array_data.shape[0]):
                    new_coordinate = Coordinate(array_data[i],
                                                coordinate_system_type=coordinate_system_type,
                                                length_unit_type=length_unit_type)
                    self._list.append(new_coordinate)
            elif isinstance(array_data, Sequence):
                for data in array_data:
                    if isinstance(data, Iterable):
                        new_coordinate = Coordinate(data,
                                                    coordinate_system_type=coordinate_system_type,
                                                    length_unit_type=length_unit_type)
                        self._list.append(new_coordinate)
                    elif isinstance(data, Coordinate):
                        if coordinate_system_type != data.system:
                            logger.warning(f'The input coordinate_system_type={coordinate_system_type} is ignored; '
                                           f'The value {data.system} from Coordinate is used.')
                        self._list.append(deepcopy(data))
                    else:
                        raise NotImplementedError
            else:
                raise NotImplementedError

    def _get_ndarray(
            self,
            length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER) -> npt.NDArray[np.float64]:

        coordinate_list = []
        for coordinate in self._list:
            coordinate_list.append(coordinate._get_xyz_array(length_unit_type=length_unit_type))
        return np.array(coordinate_list)

    @property
    def system(self) -> Optional[Coordinate.CoordinateSystem]:
        """Return the coordination system of the coordinate array.

        Return None if no data or >1 systems exist.
        """
        ret = None
        for coordinate in self._list:
            if coordinate.system != ret:
                if ret is not None:
                    return None
                ret = coordinate.system
        return ret

    def get_xyz_m_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps,[x, y, z]), xyz in meter.

        Returns:
            a new value in (n_steps,[x, y, z]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.METER)

    def get_xyz_cm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps,[x, y, z]), xyz in cm.

        Returns:
            a new value in (n_steps,[x, y, z]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.CENTIMETER)

    def get_xyz_mm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in (n_steps,[x, y, z ]), xyz in mm.

        Returns:
            a new value in (n_steps,[x, y, z]).
        """
        return self._get_ndarray(length_unit_type=BaseData.LengthUnitType.MILLIMETER)
