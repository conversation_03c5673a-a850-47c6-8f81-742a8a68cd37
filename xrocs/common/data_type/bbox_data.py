from __future__ import annotations

import array
from enum import Enum
from typing import Dict, Iterable, Optional

import numpy as np
import numpy.typing as npt
from typing_extensions import Self

from xrocs.common.data_type import BaseData


class Bbox2D(BaseData[int, np.int32]):

    class UnitType(Enum):
        PIXEL = 0

    def __init__(self, data: Iterable[int], unit_type: UnitType = UnitType.PIXEL) -> None:
        """

        Args:
            data: the data form can be (4, ):
                (x_min, y_min, x_max, y_max)
            unit_type:
        """
        self._data: array.array[int] = array.array('h', data)
        assert len(self._data) == 4, 'The shape is not correct! Expect (4, )'

        self._unit_type = unit_type

    def __repr__(self) -> str:
        return f'Bbox2D: pixel={self._data.tolist() if self._data is not None else None}'

    def _get_array(self, unit_type: UnitType = UnitType.PIXEL) -> array.array[int]:
        return self._data

    @property
    def unit(self) -> UnitType:
        """Return the unit of the coordinate."""
        return self._unit_type


class Bbox3D(BaseData[float, np.float64]):

    def __init__(self,
                 data: Iterable[float],
                 *,
                 length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER) -> None:
        """

        Args:
            data: [bbx length, bby length, bbz length]
            length_unit_type: the unit type can be MILLIMETER or CENTIMETER or METER
        """
        self._mm_data: Optional[array.array[float]] = None
        self._cm_data: Optional[array.array[float]] = None
        self._m_data: Optional[array.array[float]] = None

        if length_unit_type == BaseData.LengthUnitType.METER:
            self._m_data = array.array('f', data)
            assert len(self._m_data) == 3, 'The shape is not correct! Expect (3, )'
        elif length_unit_type == BaseData.LengthUnitType.CENTIMETER:
            self._cm_data = array.array('f', data)
            assert len(self._cm_data) == 3, 'The shape is not correct! Expect (3, )'
        elif length_unit_type == BaseData.LengthUnitType.MILLIMETER:
            self._mm_data = array.array('f', data)
            assert len(self._mm_data) == 3, 'The shape is not correct! Expect (3, )'
        else:
            raise NotImplementedError
        self._unit_type = length_unit_type

    def __repr__(self) -> str:
        return (f'Bbox3D: m={self._m_data.tolist() if self._m_data is not None else None}, '
                f'cm={self._cm_data.tolist() if self._cm_data is not None else None}, '
                f'mm={self._mm_data.tolist() if self._mm_data is not None else None}. ')

    def __deepcopy__(self, memo: Optional[Dict[str, int]] = None) -> Bbox3D:
        if self._m_data is not None:
            return Bbox3D(data=self._m_data, length_unit_type=BaseData.LengthUnitType.METER)

        if self._cm_data is not None:
            return Bbox3D(data=self._cm_data, length_unit_type=BaseData.LengthUnitType.CENTIMETER)

        if self._mm_data is not None:
            return Bbox3D(data=self._mm_data, length_unit_type=BaseData.LengthUnitType.MILLIMETER)

        raise ValueError

    def _get_xyz_length_array(self,
                              length_unit_type: BaseData.LengthUnitType = BaseData.LengthUnitType.METER
                              ) -> array.array[float]:
        data_array = self._convert_array_data_length_unit(meter_data=self._m_data,
                                                          centimeter_data=self._cm_data,
                                                          millimeter_data=self._mm_data,
                                                          to_unit_type=length_unit_type)
        if length_unit_type == BaseData.LengthUnitType.METER:
            self._m_data = data_array
            return self._m_data
        elif length_unit_type == BaseData.LengthUnitType.CENTIMETER:
            self._cm_data = data_array
            return self._cm_data
        elif length_unit_type == BaseData.LengthUnitType.MILLIMETER:
            self._mm_data = data_array
            return self._mm_data
        else:
            raise NotImplementedError

    @property
    def x_length_m(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.METER)[0]

    @property
    def y_length_m(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.METER)[1]

    @property
    def z_length_m(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.METER)[2]

    @property
    def x_length_cm(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[0]

    @property
    def y_length_cm(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[1]

    @property
    def z_length_cm(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER)[2]

    @property
    def x_length_mm(self) -> float:
        """Return the x value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[0]

    @property
    def y_length_mm(self) -> float:
        """Return the y value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[1]

    @property
    def z_length_mm(self) -> float:
        """Return the z value of coordinate."""
        return self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER)[2]

    @property
    def unit(self) -> BaseData.LengthUnitType:
        """Return the unit of the coordinate."""
        return self._unit_type

    def get_xyz_length_m_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in meter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.METER))

    def get_xyz_length_cm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in centimeter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.CENTIMETER))

    def get_xyz_length_mm_ndarray(self) -> npt.NDArray[np.float64]:
        """Return value in [x, y, z ], xyz in millimeter.

        Returns:
            a new value in [x, y, z ].
        """
        return np.array(self._get_xyz_length_array(length_unit_type=BaseData.LengthUnitType.MILLIMETER))
