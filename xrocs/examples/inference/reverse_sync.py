import time
import numpy as np
from xrocs.common.data_type import Joints
from xrocs.core.config_loader import ConfigLoader
from xrocs.utils.logger.logger_loader import logger
from xrocs.core.station_loader import StationLoader
from xtele.core.integrate_module import TeleCore
from xrocs.core.app_base import RunnerBase
from xrocs.core.fake_model import FakeModel


class ReverseInfer:
    def __init__(self, config_path = None, model_path = None):
        if config_path == None:
            config_path = "/home/<USER>/Documents/configuration.toml"
        cfg_loader = ConfigLoader(config_path)
        self.cfg_dict = cfg_loader.get_config()
        station_loader = StationLoader(self.cfg_dict)
        self.robot_station = station_loader.generate_station_handle()
        self.tele_agent = TeleCore()
        self.runner = RunnerBase(self.tele_agent, self.robot_station)
        self.robot_station.connect()
        self.infer_model = FakeModel()

    def prepare(self):
        """
        Prepare for station initialization
        """
        runner = self.runner
        runner.reset_to_home(self.cfg_dict)
        runner.check_pre_sync_status()
        runner.sync_agent_to_env()
        runner.start_keyboard_listener()
        logger.success("Prepare finished.")
    
    def sync(self):
        obs = self.robot_station.get_obs()
        while not self.runner.btn_exit:
            if self.runner.btn_reverse:
                self.tele_agent.switch_reverse()
                action_pred: np.ndarray = self.infer_model.infer(obs)
                robot_targets = self.robot_station.decompose_action(action_pred)
                obs = self.robot_station.step(robot_targets)
                self.tele_agent.sync_position(action_pred)

            elif not self.runner.btn_reverse:
                self.tele_agent.switch_act()
                robot_targets = self.robot_station.decompose_action(self.tele_agent.act())
                obs = self.robot_station.step(robot_targets)


if __name__ == '__main__':
    data = ReverseInfer()
    data.prepare()
    data.sync()
