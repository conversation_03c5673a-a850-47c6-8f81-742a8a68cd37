import time
import numpy as np
from xrocs.common.data_type import Joints
from xrocs.core.config_loader import ConfigLoader
from xrocs.utils.logger.logger_loader import logger
from xrocs.core.station_loader import StationLoader

# 导入模型class
from delpoyment.inference_frank_neil_diffusion_test import DiffusionPolicyInference


class JointInference:
    def __init__(self, config_path = None, model_path = None):
        if config_path == None:
            config_path = "/home/<USER>/Documents/configuration.toml"
        cfg_loader = ConfigLoader(config_path)
        self.cfg_dict = cfg_loader.get_config()
        station_loader = StationLoader(self.cfg_dict)
        self.robot_station = station_loader.get_station_handle()
        self.robot_station.connect()
        self.infer_model = DiffusionPolicyInference(model_path)

    def prepare(self):
        for name, _robot in self.robot_station.get_robot_handle().items():
            home = Joints(self.cfg_dict['robot']['arm']['home'][name],
                          num_of_dofs=len((self.cfg_dict['robot']['arm']['home'][name])))
            _robot.reach_target_joint(home)
        for gripper in self.robot_station.get_gripper_handle().values():
            gripper.open()
        time.sleep(2)
        logger.success('Resetting to home success!')

    def inference(self, data_dir: str, task_name: str):
        obs = self.robot_station.get_obs()
        while True:
            action_pred: np.ndarray = self.infer_model.infer(obs)
            action_pred = action_pred[0].cpu().numpy()

            robot_targets = self.robot_station.decompose_action(action_pred)
            obs = self.robot_station.step_ee(robot_targets)


if __name__ == '__main__':
    config_path = '~/Documents/configuration.toml'
    data = JointInference('/home/<USER>/ckpt/pretrained_model')
    data.prepare()
    data.inference('/home/<USER>/data/example_dir',"example_task")
