# ~/.config/xhumanoid/xrocs/default.toml

[basic]
# single_franka, franka_sim, franka_dual, franka_rs_dual
# ur_sg, ur_dual, ur_dexhand
# tienkung_robotiq, tienkung_inspire, tienkung_max, prod4wholebody
# agilex_v1, agilex_v2, arxbot
station_type = "ur_dexhand"


[collect]
save_type = 'pickle'  # pickle, hdf5
is_display_image = true

[robot]
[robot.arm.home]
left = [1.5708, -1.5708, 1.5708, -1.5708, 1.5708, 0]
right = [1.5708, -1.5708, 1.5708, -1.5708, 1.5708, 0]


[robot.arm.ip]
left = "************"
right = "************"

[robot.hand.ip]
left = "************"
right = "************"


[camera] # Orbbec
[camera.left]
port = 4277
serial = []
freq = []
size = [720, 1280]

[camera.right]
port = 4278
serial = []
freq = []
size = [480, 640]

[camera.top]
port = 4279
serial = []
freq = []
size = [480, 640]

[camera.front]
port = 4280
serial = []
freq = []
size = [720, 1280]

[camera.wrist_left]
port = 4281
serial = []
freq = []
size = [480, 640]

[camera.wrist_right]
port = 4282
serial = []
freq = []
size = [480, 640]
