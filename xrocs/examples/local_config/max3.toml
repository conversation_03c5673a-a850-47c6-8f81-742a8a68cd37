# ~/.config/xhumanoid/xrocs/default.toml

[basic]
# franka_sg, franka_sim, franka_dual, franka_rs_dual
# ur_sg, ur_dual, ur_dexhand
# tienkung_robotiq, tienkung_inspire, tienkung_max, prod4wholebody
# agilex_v1, agilex_v2, arxbot
station_type = "tienkung_max"  

[collect]
save_type = 'pickle'  # pickle, hdf5
is_display_image = true

[robot]
[robot.arm.home]
robot = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
left = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
right = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

[robot.arm.ip]
robot = "127.0.0.1"

[robot.hand.ip]
left = "127.0.0.1"
right = "127.0.0.1"

[camera]
[camera.ob_camera_head]
port = []
serial = []
freq = []
size = []

[camera.ob_camera_left]
port = []
serial = []
freq = []
size = []

[camera.ob_camera_right]
port = []
serial = []
freq = []
size = []