# ~/.config/xhumanoid/xrocs/default.toml

[basic]
# single_franka, franka_sim, franka_dual, franka_rs_dual
# ur_sg, ur_dual, ur_dexhand
# tienkung_robotiq, tienkung_inspire, tienkung_max, prod4wholebody
# agilex_v1, agilex_v2, arxbot
station_type = "single_franka"


[collect]
save_type = 'pickle'  # pickle, hdf5


[robot]
[robot.arm.home]
single = [0.0, 0.0, 0.0, -1.5707963267948966, 0.0, 1.5707963267948966, 0]

[robot.arm.ip]
single = "************"

[robot.hand.ip]
single = "************"


[camera]
[camera.left]
port = 4278
serial = 247122074163
freq = 15
size = [640, 480]

[camera.right]
port = 4279
serial = 310522072936
freq = 15
size = [640, 480]

[camera.top]
port = 4280
serial = 310522070120
freq = 15
size = [640, 480]

[camera.wrist]
port = 4281
serial = 310522072898
freq = 15
size = [640, 480]
