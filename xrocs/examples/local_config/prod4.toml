# ~/.config/xhumanoid/xrocs/default.toml

[basic]
# franka_sg, franka_sim, franka_dual, franka_rs_dual
# ur_sg, ur_dual, ur_dexhand
# tienkung_robotiq, tienkung_inspire, prod4wholebody
# agilex_v1, agilex_v2, arxbot
station_type = "tienkung_robotiq"
is_use_interpolator = false


[collect]
path = "/home/<USER>/data/2025-03-18"
[collect.task.ur2_open_oven_ur]
description = "打开烤箱"


[robot]
[robot.arm.home]
robot = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
left = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
right = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

[robot.arm.ip]
robot = "************"

[robot.hand.ip]
left = "************"
right = "************"


[camera]
[camera.dict]
top = 'camera'