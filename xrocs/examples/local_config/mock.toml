# ~/.config/xhumanoid/xrocs/default.toml

[basic]
# franka_sg, franka_sim, franka_dual, franka_rs_dual
# ur_sg, ur_dual, ur_dexhand
# tienkung_robotiq, tienkung_inspire, tienkung_max, prod4wholebody
# agilex_v1, agilex_v2, arxbot
station_type = "mock"  

[robot]
[robot.arm.home]
robot = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

[robot.arm.ip]
robot = "127.0.0.1"

[robot.hand.ip]
robot = "127.0.0.1"

[camera]
[camera.dict]
ob_camera_head = 'ob_camera_head'