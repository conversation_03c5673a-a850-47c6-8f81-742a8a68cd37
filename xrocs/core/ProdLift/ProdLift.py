import time
from typing import Optional
import numpy as np
import rospy
import tqdm
from sensor_msgs.msg import JointState
from std_msgs.msg import Header
from xrocs.common.data_type import Pose, Joints
from xrocs.entity.robot.robot_base import RobotArmDriver
from xrocs.utils.FlexJoints import Flex
from bodyctrl_msgs.msg import MotorStatusMsg, CmdSetMotorPosition, SetMotorPosition


class TienkungRobot(RobotArmDriver):
    def __init__(self, robot_ip: str, local_ip: str):
        rospy.init_node("liftControlNode")
        self.waist_publisher = rospy.Publisher('/waist/cmd_pos', CmdSetMotorPosition, queue_size = 10)
        rospy.Subscriber('/waist/status', MotorStatusMsg, self._waist_callback)
        self.lift_pos = None
        for _ in tqdm.tqdm(range(3), desc="Warm Up Node"):
            time.sleep(1)
        self.arm_pos = np.concatenate([self.left_jpos, self.right_jpos, self.waist_pos])
        assert len(self.arm_pos) == self.num_dofs, "Tienkung miss arm or waist msgs."
        self.rate = rospy.Rate(400)
        self.flex_server = Flex(self.num_dofs(), window_size=30, init_state=self.arm_pos)

    def num_dofs(self) -> int:
        return 15

    def connect(self) -> bool:        
        return True

    def _left_arm_callback(self, data):
        self.left_jpos = list(data.position)

    def _right_arm_callback(self, data):
        self.right_jpos = list(data.position)
    
    def _waist_callback(self, data):
        self.waist_pos = list(data.status[0].pos)

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.concatenate([self.left_jpos, self.right_jpos, self.waist_pos]), num_of_dofs=self.num_dofs())

    @staticmethod
    def _construct_dual_arm_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.name = [str(i) for i in range(1, 15)]
        msg.position = target_joint
        msg.velocity = [0] * 14
        return msg

    def reach_target_joint(self, target_joint: list[float], asynchronous: bool = False) -> bool:
        fine_step = 200
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 np.array(target_joint), fine_step)
        for stp in step_array:
            self.dual_arm_publisher.publish(self._construct_dual_arm_msg(stp[:-1]))
            self.waist_publisher.publish(self._construct_dual_arm_msg(stp[-1]))
            self.rate.sleep()
        return True

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        return None

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        raise NotImplementedError("Not Implemented: def reach_tool_cartesian_pose()")

    def sync_target_joint(self, target_joint: np.ndarray):
        filtered_target = self.flex_server.update(target_joint.tolist())
        arm_target = filtered_target[:-1]
        waist_target = filtered_target[14]
        self.dual_arm_publisher.publish(self._construct_dual_arm_msg(arm_target))
        self.waist_publisher.publish(self._construct_waist_msg(waist_target))
        
    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        raise NotImplementedError("Not Implemented: def sync_tool_cartesian_pose()")
    
    def _construct_waist_msg(self, target_joints: float):
        msg = CmdSetMotorPosition()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.header.seq = self.seq
        msg.header.frame_id = 'waist'
        t_cmd = SetMotorPosition()
        t_cmd.name = 11
        t_cmd.pos = target_joints
        t_cmd.spd = 0.3
        msg.cmds = [t_cmd]
        self.seq += 1
        return msg

