import time
import threading
import queue
import numpy as np

class MotionInterpolator:
    def __init__(self, dof):
        self.dof = dof
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.last_waypoint = None
        self.last_time = None
        self.last_output = np.zeros(dof)
        self.running = False
        self.lock = threading.Lock()
        
        # 初始化线程
        self.interp_thread = threading.Thread(target=self._interpolation_worker)
        self.output_thread = threading.Thread(target=self._output_worker)

    def start(self):
        self.running = True
        self.interp_thread.start()
        self.output_thread.start()

    def stop(self):
        self.running = False
        self.interp_thread.join()
        self.output_thread.join()

    def add_waypoint(self, waypoint):
        """添加新的路径点"""
        with self.lock:
            self.input_queue.put(np.array(waypoint))

    def _quintic_interpolation(self, p0, p1, t, T):
        """五次多项式插值计算"""
        t /= T
        return p0 + (p1 - p0) * (10*t**3 - 15*t**4 + 6*t**5)

    def _interpolation_worker(self):
        """插值计算线程"""
        while self.running:
            try:
                # 获取新路径点
                new_waypoint = self.input_queue.get(timeout=0.01)
                current_time = time.time()

                if self.last_waypoint is not None:
                    # 计算时间间隔和需要生成的插值点数
                    dt = current_time - self.last_time
                    num_points = max(1, int(round(dt * 200)))  # 保证至少1个点
                    actual_dt = num_points * 0.005  # 实际插值时间

                    # 生成插值点
                    for i in range(1, num_points+1):
                        t = i * 0.005  # 精确的5ms间隔
                        interp_point = self._quintic_interpolation(
                            self.last_waypoint,
                            new_waypoint,
                            t,
                            actual_dt
                        )
                        self.output_queue.put(interp_point)

                # 更新状态
                with self.lock:
                    self.last_waypoint = new_waypoint
                    self.last_time = current_time

            except queue.Empty:
                continue

    def _output_worker(self):
        """稳定输出线程"""
        interval = 0.005  # 200Hz = 5ms
        next_time = time.time()

        while self.running:
            # 获取当前点
            try:
                point = self.output_queue.get(timeout=0.001)
                self.last_output = point
            except queue.Empty:
                point = self.last_output

            # 这里添加实际的硬件控制接口
            self._send_to_robot(point)

            # 维持精确的200Hz频率
            next_time += interval
            sleep_time = next_time - time.time()
            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                next_time = time.time()  # 追不上时重置时间基准

    def _send_to_robot(self, command):
        """实际发送指令到机械臂的接口（需根据实际情况实现）"""
        print(f"Send command: {np.round(command, 4)} at {time.time():.3f}")

if __name__ == "__main__":
    controller = MotionInterpolator(dof=6)
    controller.start()

    for i in range(5):
        waypoint = np.sin(i*0.1) * np.ones(6)
        controller.add_waypoint(waypoint)
        time.sleep(0.1)

    time.sleep(1)
    controller.stop()