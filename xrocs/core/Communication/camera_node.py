import pickle
import threading
from typing import Optional, <PERSON><PERSON>
import time
import numpy as np
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.camera.camera_base import Camera<PERSON>river
import zmq

DEFAULT_CAMERA_PORT = 5000
class ZMQClientCamera(CameraDriver):
    """A class representing a ZMQ client for a leader robot."""

    def __init__(
            self, 
            port: int = DEFAULT_CAMERA_PORT, 
            host: str = "127.0.0.1",
            publist_fps: int = 30
        ):
        self._context = zmq.Context()
        self._socket = self._context.socket(zmq.REQ)
        self._socket.connect(f"tcp://{host}:{port}")

    def read(
        self,
        img_size: Optional[Tuple[int, int]] = None,
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Get the current state of the leader robot.
        Returns:
            T: The current state of the leader robot.
        """
        send_message = pickle.dumps(img_size)
        self._socket.send(send_message)
        state_dict = pickle.loads(self._socket.recv())
        return state_dict


class ZMQServerCamera:
    def __init__(
        self,
        camera: CameraDriver,
        config: dict,
        host: str = "127.0.0.1",
    ):
        self._camera = camera
        self.freq = config['freq']
        self._context = zmq.Context()
        self._socket = self._context.socket(zmq.REP)
        self.port = config['port']
        addr = f"tcp://{host}:{self.port}"
        debug_message = f"Camera Sever Binding to {addr}, Camera: {camera}"
        logger.info(debug_message)
        self._timeout_message = f"Timeout in Camera Server, Camera: {camera}"
        self._socket.bind(addr)
        self._stop_event = threading.Event()
        self._publish_interval = 1.0 / self.freq
        self._camera.read()

    def serve(self) -> None:
        """Serve the leader robot state over ZMQ."""
        self.last_time = time.time()
        while not self._stop_event.is_set():
            try:
                message = self._socket.recv()
                img_size = pickle.loads(message)
                camera_data = self._camera.read()
                self._socket.send(pickle.dumps(camera_data))
                print(f"Freq on {self.port} is {round(100/(time.time() - self.last_time))/100} ")
                self.last_time = time.time()
            except zmq.Again:
                print(self._timeout_message)

    def stop(self) -> None:
        """Signal the server to stop serving."""
        self._stop_event.set()
