import rospy
from sensor_msgs.msg import Joy
from std_msgs.msg import Head<PERSON>
from pynput import keyboard
import threading
import time
import numpy as np
from tqdm import *


class ChassisCalibrate():
    def __init__(self):
        rospy.Subscriber("/sbus_data", Joy, self._joy_callback, queue_size=10)  # hz~=43.2
        self.joy_state_move = None
        self.joy_state_turn = None
        self.is_start = False
        self.is_stop = False
        self.is_exit = False
        self.m_type = '0'

        self.coor_move = 0     #2.14
        self.coor_turn = 0     #1.2s

        self.call_freq = 100

        for i in tqdm(range(10)):
            time.sleep(0.1)
    
    def _joy_callback(self, data):
        self.joy_state_move = data.axes[2]
        self.joy_state_turn = data.axes[3]

    def start_cali(self):
        print("Press s for start, press q for stop, press esc for end program.")
        while True:
            if self.is_start:
                self.is_start = False
                self.is_stop = False
                if m_type == '1':
                    self._process_and_cal_coor_move()
                elif m_type == '2':
                    self._process_and_cal_coor_turn()
                m_type == '0'
                print("Press s for start, press q for stop, press esc for end program.")
            if self.is_exit:
                return

    def start_keyboard_listener(self):
        def _listener():
            with keyboard.Listener(on_press=self._on_press) as listener:
                listener.join()
        self.listener_thread = threading.Thread(target=_listener, daemon=True)
        self.listener_thread.start()

    def _on_press(self, key: keyboard.KeyCode):
        try:
            if key.char == 's':
                print("\nin start")
                self.is_start = True
            if key.char == 'q':
                print("in stop")
                self.is_stop = True
            if key == keyboard.Key.esc:
                self.is_exit = True
        except AttributeError as e:
            print(f"Keyboard AttributeError:{e}")
    

    def _process_and_cal_coor_move(self) -> bool:
        print("In calibrating moving coor.---------")
        integrad_moving = 0
        while True:
            t_start_this_cycle = time.time()
            time.sleep(float(1/self.call_freq))
            integrad_moving = integrad_moving + (time.time()-t_start_this_cycle) * self.joy_state_move
            if self.is_stop:
                real_moving = 1
                self.coor_move = integrad_moving / real_moving
                print("\nCalied moving coor is: ", np.around(self.coor_move, decimals=4))
                return True
    
    def _process_and_cal_coor_turn(self):
        print("In calibrating moving coor.---------")
        integrad_turning = 0
        while True:
            t_start_this_cycle = time.time()
            time.sleep(float(1/self.call_freq))
            integrad_turning = integrad_turning + (time.time()-t_start_this_cycle) * self.joy_state_turn
            if self.is_stop:
                real_turning = np.pi/2
                self.coor_turn = integrad_turning / real_turning
                print("\nCalied moving coor is: ", np.around(self.coor_turn, decimals=4))
                return True

if __name__ == '__main__':
    rospy.init_node("Joy_listener_node")
    m_chassis_cali = ChassisCalibrate()
    m_type = input("Select cali param( 1 for move, 2 for turn): ")
    m_chassis_cali.m_type = m_type
    m_chassis_cali.start_keyboard_listener()
    m_chassis_cali.start_cali()