import numpy as np
from xrocs.core.config_loader import ConfigLoader

class FakeModel:
    def __init__(self, model_path = None):
        cfg_loader = ConfigLoader()
        self.cfg_dict = cfg_loader.get_config()
        # self.len_left = len(self.cfg_dict['robot']['arm']['home']['left'])
        # self.len_right = len(self.cfg_dict['robot']['arm']['home']['right'])
        self.len_single = len(self.cfg_dict['robot']['arm']['home']['single'])


    def infer(self, obs = None):
        # left_target = np.concatenate((np.array([self.cfg_dict['robot']['arm']['home']['left']]), np.array([0])))
        # right_target = np.concatenate((np.array([self.cfg_dict['robot']['arm']['home']['right']]), np.array([0])))
        single_target = np.append(np.array(self.cfg_dict['robot']['arm']['home']['single']), 0) 
        return single_target