import threading
import time
import numpy as np
from pynput import keyboard
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.core.audio.tts_speaker import ChatTT<PERSON><PERSON>
from xrocs.common.data_type import Joints
from xrocs.utils.logger.logger_loader import logger


class RunnerBase:
    def __init__(self, arm_agent, robot_station: RobotEnvBase):
        self.btn_preparing = True
        self.btn_stop = False
        self.btn_exit = False
        self.btn_reverse = True
        self.speaker = ChatTTSPlayer()
        self._arm_agent = arm_agent
        self._robot_std = robot_station

    def reset_to_home(self, config):
        for name, _robot in self._robot_std.get_robot_handle().items():
            home = Joints(config['robot']['arm']['home'][name],
                          num_of_dofs=len((config['robot']['arm']['home'][name])))
            _robot.reach_target_joint(home)
        for gripper in self._robot_std.get_gripper_handle().values():
            gripper.open()
        time.sleep(2)
        logger.success('Resetting to home success!')
        
    def check_pre_sync_status(self, stuck=True):
        obs = self._robot_std.get_obs()
        act = self._robot_std.decompose_action(self._arm_agent.act())
        for name, val in obs['arm_joints'].items():
            abs_deltas = np.abs(val - act['arm_joints'][name])
            id_max_joint_delta = np.argmax(abs_deltas)
            max_joint_delta = 0.8
            if abs_deltas[id_max_joint_delta] > max_joint_delta:
                id_mask = abs_deltas > max_joint_delta
                ids = np.arange(len(id_mask))[id_mask]
                for i, delta, joint, current_j in zip(
                    ids,
                    abs_deltas[id_mask],
                    act['arm_joints'][name][id_mask],
                    val[id_mask]
                ):
                    logger.info(
                        f"joint[{i}]: \t delta: {delta:4.3f} , leader: \t{joint:4.3f} , follower: \t{current_j:4.3f}"
                    )         
                if stuck:
                    assert False, "Joint delta is too big"
        logger.success('pre-check success!')
        
    def sync_agent_to_env(self):
        self.speaker.speak("同步中，请尽量保持不动")
        def _cal_delta(target, current):
            max_delta = 0.05
            _delta = target - current
            max_joint_delta = np.abs(_delta).max()
            if max_joint_delta > max_delta:
                _delta = _delta / max_joint_delta * max_delta
            return _delta
        obs = self._robot_std.get_obs()
        for _ in range(20):
            act = self._robot_std.decompose_action(self._arm_agent.act())
            arm_targets = {'arm_joints': {}}
            hand_targets = {'hand_joints': {}}
            for name in obs['arm_joints'].keys():
                delta = _cal_delta(act['arm_joints'][name], obs['arm_joints'][name])
                arm_targets['arm_joints'][name] = obs['arm_joints'][name] + delta
            for name in obs['hand_joints'].keys():
                delta = _cal_delta(act['hand_joints'][name], obs['hand_joints'][name])
                hand_targets['hand_joints'][name] = obs['hand_joints'][name] + delta
            robot_targets = {}
            robot_targets.update(arm_targets)
            robot_targets.update(hand_targets)
            obs = self._robot_std.step(robot_targets)
        self.speaker.speak("同步完成，准备开始")

    def start_keyboard_listener(self):
        def _listener():
            with keyboard.Listener(on_press=self.on_press) as listener:
                listener.join()
        listener_thread = threading.Thread(target=_listener, daemon=True)
        listener_thread.start()

    def on_press(self, key):
        try:
            if key == keyboard.Key.scroll_lock:
                self.btn_preparing = False
            if key == keyboard.Key.pause:
                self.btn_stop = True
            if key == keyboard.Key.esc:
                self.btn_exit = True
            if key == keyboard.Key.delete:
                self.btn_reverse = not self.btn_reverse
        except AttributeError:
            logger.error("Keyboard AttributeError")
