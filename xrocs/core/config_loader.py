import os
import argparse
from typing import Any
import tomli as tomllib

__all__ = ['config', 'config_loader']

class ConfigLoader:
    def __init__(self, config_path = None) -> None:
        if config_path is None or config_path == "-":
            config_path = os.path.expanduser("~/Documents/configuration.toml")
        with open(config_path, 'rb') as f:
            self.config = tomllib.load(f)
            
    def get_config(self):
        return self.config
    
config_loader = ConfigLoader()
config = config_loader.config
