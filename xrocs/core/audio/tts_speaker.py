import time
import os.path as osp
import abc
from threading import Thread, Lock

import requests
import vlc
from loguru import logger

class BaseTTSPlayer:
    def __init__(self) -> None:
        self.voice_player = vlc.MediaPlayer()
        self.lock = Lock()  # 锁控线程同步

    @abc.abstractmethod
    def text_to_speech(self, text):
        pass

    def speak(self, text, sync=False, block=True):
        if not sync:
            _reading_thread = Thread(target=self._speak, args=(text, sync, block))
            _reading_thread.daemon = True
            _reading_thread.start()
        else:
            self._speak(text, sync, block)

    def _speak(self, text, sync=False, block=False):
        with self.lock:  #
            if text is None or text.strip() == "":
                return

            text = text.strip()
            if osp.exists(text):
                voice_url = text
            else:
                voice_url = self.text_to_speech(text)

            print(f"voice_url: {voice_url}")
            if block and self.voice_player.is_playing():
                self.voice_player.stop()

            if not block:
                while self.voice_player.is_playing():
                    time.sleep(0.05)  # 等待当前语音播放完成

            self.voice_player.set_mrl(voice_url)
            self.voice_player.play()
            if sync:
                while self.voice_player.is_playing():
                    time.sleep(0.05)

            return voice_url

class ChatTTSPlayer(BaseTTSPlayer):
    def __init__(self, url='http://*************:9966/tts', voice_file="2222.pt", timeout=60):
        super().__init__()
        self.url = url
        self.voice_file = voice_file
        self.timeout = timeout

    def text_to_speech(self, text):
        answer = None
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {
            "text": text,
            "prompt": "[break_6]",
            "voice": self.voice_file,
            "speed": 3,
            "temperature": 0.1,
            "top_p": 0.701,
            "top_k": 20,
            "text_seed": 5,
            "skip_refine": 1,
            "custom_voice": 0
        }

        start_time = time.time()
        try:
            response = requests.post(self.url, data=data, headers=headers, timeout=self.timeout)
        except requests.exceptions.ConnectionError:
            logger.error(f"ChatTTSPlayer: 连接ChatTTS服务超时，语音合成失败，请检查服务及网络: {self.url}")
            return answer
        except Exception as err:
            logger.error(f"ChatTTSPlayer: {err}")
            return answer

        cost = (time.time() - start_time) * 1000
        try:
            resp_json = response.json()
            if resp_json["code"] == 0:
                answer = resp_json["audio_files"][0]["url"]
            else:
                logger.error(f"ChatTTSPlayer: 合成失败：{resp_json['msg']}")
        except Exception as err:
            logger.error(f"ChatTTSPlayer: 错误，返回的内容不是合法json格式")

        logger.info(f"ChatTTSPlayer: cost: {cost:.2f} ms")
        return answer


if __name__ == "__main__":
    speaker = ChatTTSPlayer()
    #测试复杂情况
    speaker.speak("这是一个新的任务", sync=False, block=False)
    speaker.speak("今天天气很好今天天气很好", sync=True, block=False)
    speaker.speak("我吃饭了我吃饭了我吃饭了", sync=False, block=True)
    speaker.speak("中断当前同步任务，开始新任务", sync=False, block=False)
    speaker.speak("同步中同步中同步中同步中", sync=True, block=False)

    time.sleep(15)
