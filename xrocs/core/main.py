import os.path
from xrocs.core.config_loader import Config<PERSON>oader

def main(args):
    mission = args.mode
    config_path = args.config
    cfg_loader = ConfigLoader(config_path)
    cfg_dict = cfg_loader.get_config()
    if mission == 'collect':
        from xrocs.apps.data_collection.DataCollector import DataCollector
        collector = DataCollector(config_path)
        collector.prepare()
        DEFAULT_PATH = os.path.join(os.getenv('HOME'), 'data')
        collector.start_collect(os.path.join(DEFAULT_PATH, 'tmp'),
                       [
                           {
                               "name": "ur2_place_eggplant_to_plate",
                               "description": "把茄子放进盘子"              
                           },
                        ])
    elif mission == 'cali':
        from xrocs.apps.camera_calibration.StationCali import StationCali
        collector = StationCali(config_path)
        collector.prepare()
        collector.calibrate()
    elif mission == "openconfig":
        os.system("code ~/Documents/configuration.toml")
        
    elif mission == "check" or mission == "check_all":
        # Check all devices status
        from xrocs.apps.status_checking.check import check_hardware
        check_hardware(cfg_dict)
    elif mission == "check_franka":
        from xrocs.apps.status_checking.check_franka import check_franka
        check_franka(cfg_dict)
    elif mission == "check_ur":
        from xrocs.apps.status_checking.check_ur import check_ur
        check_ur(cfg_dict)
    elif mission == "check_realsense" or mission == "check_rs":
        from xrocs.apps.status_checking.check_realsense import check_realsense
        check_realsense(cfg_dict)
    elif mission == "check_orbbec":
        from xrocs.apps.status_checking.check_orbbec import check_orbbec
        check_orbbec(cfg_dict)

    else:
        raise ValueError(f"Unknown mission type: {mission}")
