from xrocs.utils.logger.logger_loader import logger

class StationLoader:
    def __init__(self, cfg_dict : dict = {}):
        self.cfg_dict = cfg_dict

        if "is_use_interpolator" in self.cfg_dict["basic"] and self.cfg_dict["basic"]["is_use_interpolator"] == True:
            self.is_intrp = True
        else:
            self.is_intrp = False

    def generate_station_handle(self):
        config = self.cfg_dict
        if 'single_franka' == config['basic']['station_type']:
            from xrocs.entity.station.FrankaStd.franka_sg_station import FrankaSgStation
            robot_station = FrankaSgStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'franka_sim' in config['basic']['station_type']:
            from xrocs.entity.station.FrankaSim.franka_sim_station import FrankaSimStation
            robot_station = FrankaSimStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'franka_dual' in config['basic']['station_type']:
            from xrocs.entity.station.FrankaStd.franka_orb_station import FrankaStdStation
            robot_station = FrankaStdStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'franka_rs_dual' in config['basic']['station_type']:
            from xrocs.entity.station.FrankaStd.franka_rs_dual import FrankaDualRS
            robot_station = FrankaDualRS(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'ur_sg' == config['basic']['station_type']:
            from xrocs.entity.station.URStd.ur_std_station import URStdStation
            robot_station = URStdStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'ur_dual' == config['basic']['station_type']:
            from xrocs.entity.station.URStd.ur_std_station import URStdStation
            robot_station = URStdStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'ur_dexhand' == config['basic']['station_type']:
            from xrocs.entity.station.URDexhand.ur_dexhand_station import URDexhandStation
            robot_station = URDexhandStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'tienkung_inspire' == config['basic']['station_type']:
            from xrocs.entity.station.XProD.x_prod_hand import XProDHand
            robot_station = XProDHand(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'tienkung_robotiq' == config['basic']['station_type']:
            from xrocs.entity.station.XProD.x_prod_gripper import XProDGripper
            robot_station = XProDGripper(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'tienkung_max' == config['basic']['station_type']:
            from xrocs.entity.station.XProD.x_max3_gripper import XProDGripper
            robot_station = XProDGripper(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'])
        elif 'tienkung2_std' == config['basic']['station_type']:
            from xrocs.entity.station.XProD.x_2_std import X2Gripper
            robot_station = X2Gripper(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'])
        elif 'agilex_v1' == config['basic']['station_type']:
            from xrocs.entity.station.Agilex_v1.agilex_station import AgilexStation
            robot_station = AgilexStation(config['robot']['arm']['ip'], config['camera'], is_intrp=self.is_intrp)
        elif 'agilex_v2' == config['basic']['station_type']:
            from xrocs.entity.station.Agilex_v2.agilex_station import AgilexStation
            robot_station = AgilexStation(config['robot']['arm']['ip'], config['camera'], is_intrp=self.is_intrp)
        elif 'arxbot' == config['basic']['station_type']:
            from xrocs.entity.station.ArxBot.arxbot_station import ArxbotStation
            robot_station = ArxbotStation(config['robot']['arm']['ip'], config['camera'], is_intrp=self.is_intrp)
        elif 'prod4wholebody' == config['basic']['station_type']:
            from xrocs.entity.station.XWholeBody.x_wholebody_station import XWholeBody
            robot_station = XWholeBody(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        elif 'mock' == config['basic']['station_type']:
            from xrocs.entity.station.Mocker.mock_station import MockStation
            robot_station = MockStation(config['robot']['arm']['ip'], config['camera'], config['robot']['hand']['ip'], is_intrp=self.is_intrp)
        else:
            raise ValueError("Invalid Station Type")
        logger.success("Work Station loaded...")
        return robot_station