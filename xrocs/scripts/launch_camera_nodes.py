import time
from dataclasses import dataclass
from multiprocessing import Process
from threading import Thread
import tyro
from xrocs.core.Communication.camera_node import ZMQServerCamera
from xrocs.entity.camera.realsense_camera import RealSenseCamera, get_device_ids
from xrocs.core.config_loader import Config<PERSON>oader

@dataclass
class Args:
    hostname: str = "127.0.0.1"
    config_path: str = ""

def launch_server(config_dict, args: Args):
    port = config_dict['port']
    try:
        camera = RealSenseCamera(config_dict)
        server = ZMQServerCamera(camera, config = config_dict, host=args.hostname)
        print(f"Starting camera server on port {port}")
        server.serve()
    except Exception as e:
        print(f"Failed to initialize camera on port {port}: {str(e)}")
        raise

def main(args):
    cfg_loader = ConfigLoader()
    cfg_dict = cfg_loader.get_config()
    camera_dict = cfg_dict["camera"]
    ids = sorted(get_device_ids())
    print(f"Found {len(ids)} cameras: {ids}")
    camera_servers = []
    for name, value in camera_dict.items():
        try:
            print(f"Launching camera {name} on port {value['port']}")
            server = Thread(target=launch_server, args=(value, args))
            server.start()
            camera_servers.append(server)
            time.sleep(1)
        except Exception as e:
            print(f"Failed to launch camera {name}: {str(e)}")
    
    for server in camera_servers:
        try:
            server.join()
        except Exception as e:
            print(f"Error in camera server thread: {str(e)}")


if __name__ == "__main__":
    main(tyro.cli(Args))
