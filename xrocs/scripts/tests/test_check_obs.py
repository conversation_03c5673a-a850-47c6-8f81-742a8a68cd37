import pickle
from pathlib import Path

root_folder = Path("/tmp/stack_cloth/data/task1")
for sub_folder in root_folder.iterdir():
    print("______________________")
    if sub_folder.is_dir():
        for file_path in sub_folder.glob("*.pkl"):
            print("--------------------")
            try:
                with open(file_path, "rb") as f:
                    obs = pickle.load(f)

                for name, value in obs.items():
                    print(name)
                    print(value)
                break
            except Exception as e:
                print(f"Failure in {file_path}: {e}")
