#!/bin/bash
set -e

# 获取脚本所在绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "\033[34m卸载旧版xrocs...\033[0m"
pip uninstall -y xrocs || true

echo -e "\033[34m安装当前版本xrocs...\033[0m"
cd "$PROJECT_ROOT"
cd ..
pip install -e .

echo -e "\033[34m处理xcollector依赖...\033[0m"
pip uninstall -y xcollector || true
cd "$PROJECT_ROOT/dependencies/xcollector"
pip install -e .

echo -e "\033[34m部署命令行工具...\033[0m"
mkdir -p ~/.local/bin
cp "$SCRIPT_DIR/xhumanoid-xrocs" ~/.local/bin/
chmod +x ~/.local/bin/xhumanoid-xrocs

echo -e "\033[34m复制配置文件...\033[0m"
mkdir -p ~/Documents
cp "$PROJECT_ROOT/examples/local_config/configuration.toml" ~/Documents/

echo -e "\n\033[32m安装成功！\033[0m"