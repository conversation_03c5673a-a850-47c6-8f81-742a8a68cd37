import pyrealsense2 as rs
import cv2
import numpy as np
from typing import List

def get_device_ids() -> List[str]:
    ctx = rs.context()
    devices = ctx.query_devices()
    device_ids = []
    for dev in devices:
        device_ids.append(dev.get_info(rs.camera_info.serial_number))
    return device_ids

print(get_device_ids())
ctx = rs.context()
devices = ctx.query_devices()
serials = [dev.get_info(rs.camera_info.serial_number) for dev in devices]

if not serials:
    print("未检测到任何RealSense设备!")
    exit()

pipelines = []
for serial in serials:
    config = rs.config()
    config.enable_device(serial)
    config.enable_stream(rs.stream.color, 1280, 720, rs.format.bgr8, 30)
    pipeline = rs.pipeline(ctx)
    pipeline.start(config)
    pipelines.append(pipeline)

try:
    rows = 2
    cols = 3
    window_w = 600
    window_h = 400
    cv2.namedWindow('Camera Grid', cv2.WINDOW_NORMAL)
    
    while True:
        images = []
        for pipeline, serial in zip(pipelines, serials):
            frames = pipeline.wait_for_frames()
            color_frame = frames.get_color_frame()
            
            if color_frame:
                img = np.asanyarray(color_frame.get_data())
                img = cv2.resize(img, (window_w, window_h))
                
                cv2.putText(img, f"S/N: {serial}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            else:
                img = np.zeros((window_h, window_w, 3), dtype=np.uint8)
                
            images.append(img)

        while len(images) < rows * cols:
            images.append(np.zeros((window_h, window_w, 3), dtype=np.uint8))

        grid = []
        for r in range(rows):
            row_start = r * cols
            row_images = images[row_start : row_start + cols]
            grid.append(np.hstack(row_images))
        
        full_image = np.vstack(grid)
        cv2.imshow('Camera Grid', full_image)
        if cv2.waitKey(1) in [ord('q'), 27]:
            break

finally:
    for p in pipelines:
        p.stop()
    cv2.destroyAllWindows()
    
    