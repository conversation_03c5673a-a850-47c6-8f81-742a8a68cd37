<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-1-g15f4949  Build Version: 1.6.7594.29634
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="rm_75_6f_description">
  <link
    name="base_link">
    <inertial>
      <origin
        xyz="0.00043296 3.5291E-05 0.059942"
        rpy="0 0 0" />
      <mass
        value="0.84105" />
      <inertia
        ixx="0.0017261"
        ixy="2.5275E-06"
        ixz="3.6769E-05"
        iyy="0.0017099"
        iyz="-1.68E-06"
        izz="0.00090402" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="Link1">
    <inertial>
      <origin
        xyz="-2.1376E-07 0.025186 -0.021108"
        rpy="0 0 0" />
      <mass
        value="0.59356" />
      <inertia
        ixx="0.0012661"
        ixy="-9.8019E-09"
        ixz="-1.2951E-08"
        iyy="0.00056135"
        iyz="-0.00021122"
        izz="0.0011817" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint1"
    type="revolute">
    <origin
      xyz="0 0 0.2405"
      rpy="-1.5708 0 0" />
    <parent
      link="base_link" />
    <child
      link="Link1" />
    <axis
      xyz="0 -1 0" />
    <limit
      lower="-3.107"
      upper="3.107"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link2">
    <inertial>
      <origin
        xyz="4.1111E-07 0.011078 0.076129"
        rpy="0 0 0" />
      <mass
        value="0.43285" />
      <inertia
        ixx="0.0012584"
        ixy="5.6315E-09"
        ixz="1.3348E-09"
        iyy="0.0012225"
        iyz="0.000279"
        izz="0.00031747" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint2"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="Link1" />
    <child
      link="Link2" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.269"
      upper="2.269"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link3">
    <inertial>
      <origin
        xyz="-4.7932E-07 0.027347 -0.023544"
        rpy="0 0 0" />
      <mass
        value="0.43132" />
      <inertia
        ixx="0.00079433"
        ixy="1.0334E-08"
        ixz="-2.329E-09"
        iyy="0.00031507"
        iyz="-0.00014262"
        izz="0.00073037" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint3"
    type="revolute">
    <origin
      xyz="0 0 0.256"
      rpy="-1.5708 0 0" />
    <parent
      link="Link2" />
    <child
      link="Link3" />
    <axis
      xyz="0 -1 0" />
    <limit
      lower="-3.107"
      upper="3.107"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link4">
    <inertial>
      <origin
        xyz="5.0444E-06 0.010569 0.059593"
        rpy="0 0 0" />
      <mass
        value="0.28963" />
      <inertia
        ixx="0.00063737"
        ixy="-3.8666E-08"
        ixz="7.0522E-08"
        iyy="0.00061418"
        iyz="0.00014461"
        izz="0.00015648" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint4"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="Link3" />
    <child
      link="Link4" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.356"
      upper="2.356"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link5">
    <inertial>
      <origin
        xyz="1.1466E-06 0.021541 -0.018041"
        rpy="0 0 0" />
      <mass
        value="0.23941" />
      <inertia
        ixx="0.00028594"
        ixy="1.8435E-09"
        ixz="2.6611E-09"
        iyy="0.00011989"
        iyz="-4.4238E-05"
        izz="0.00026273" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint5"
    type="revolute">
    <origin
      xyz="0 0 0.21"
      rpy="-1.5708 0 0" />
    <parent
      link="Link4" />
    <child
      link="Link5" />
    <axis
      xyz="0 -1 0" />
    <limit
      lower="-3.107"
      upper="3.107"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link6">
    <inertial>
      <origin
        xyz="4.5754E-06 0.0073681 0.059382"
        rpy="0 0 0" />
      <mass
        value="0.21879" />
      <inertia
        ixx="0.00035053"
        ixy="-1.7437E-08"
        ixz="3.1653E-08"
        iyy="0.00033447"
        iyz="7.824E-05"
        izz="0.00010492" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint6"
    type="revolute">
    <origin
      xyz="0 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="Link5" />
    <child
      link="Link6" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="-2.234"
      upper="2.234"
      effort="100"
      velocity="1" />
  </joint>
  <link
    name="Link7">
    <inertial>
      <origin
        xyz="0.00032231 -0.0001782 -0.028676"
        rpy="0 0 0" />
      <mass
        value="0.14433" />
      <inertia
        ixx="3.6119E-05"
        ixy="-1.3242E-06"
        ixz="-3.2733E-08"
        iyy="3.4626E-05"
        iyz="7.6694E-09"
        izz="6.1463E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="meshes/RM75_6F/Link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="joint7"
    type="revolute">
    <origin
      xyz="0 0 0.1725"
      rpy="0 0 0" />
    <parent
      link="Link6" />
    <child
      link="Link7" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="-6.283"
      upper="6.283"
      effort="100"
      velocity="1" />
  </joint>

  <link name="ee_link"/>

  <joint name="ee_fixed_joint" type="fixed">
    <parent link="Link7"/>
    <child link="ee_link"/>
    <origin rpy="0 0.0 0" xyz="0.0 0. 0.1"/>
  </joint>

</robot>
