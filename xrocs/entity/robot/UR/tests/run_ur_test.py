from xrocs.common.data_type import Coordinate, Po<PERSON>, Rotation
from xrocs.entity.robot.UR.ur import <PERSON><PERSON><PERSON><PERSON><PERSON>


def main():
    robot_ip = "***********"
    ur = URRobot(robot_ip)
    print(ur.get_tool_cartesian_pose())

    pose = ur.get_tool_cartesian_pose().get_xyz_m_rpy_radian_ndarray()
    pose[0] += 0.01
    new_pose = Po<PERSON>(Coordinate(pose[:3]), Rotation(pose[3:], rotation_form_type=Rotation.FormType.RPY))
    ur.reach_tool_cartesian_pose(new_pose)

    # joints = ur.get_arm_joints().get_radian_ndarray()
    # joints[-1] += 0.1
    # ur.reach_arm_joints(Joints(joints))


if __name__ == "__main__":
    main()
