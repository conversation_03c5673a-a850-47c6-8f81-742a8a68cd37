import traceback
from typing import Optional
import numpy as np
import rtde_control
import rtde_receive
from xrocs.common.data_type import Joints, Pose, Coordinate, Rotation
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver


class URRobot(RobotArmDriver):
    """A class representing a UR robot."""

    def __init__(self, robot_ip: str) -> None:
        self._robot_ip = robot_ip
        self._controller = None
        self._receiver = None

        # TODO: (<PERSON>) move to config
        self.tcp_speed = 0.25
        self.tcp_acceleration = 1.2
        self.joint_speed = 1.05
        self.joint_acceleration = 1.4

        self.velocity = 0.2
        self.acceleration = 0.2
        self.dt = 1.0 / 100  # 
        self.lookahead_time = 0.2
        self.gain = 100

        self.servo_running = False

    def num_dofs(self) -> int:
        return 6

    def connect(self) -> bool:
        try:
            self._controller = rtde_control.RTDEControlInterface(self._robot_ip)
            self._receiver = rtde_receive.RTDEReceiveInterface(self._robot_ip)
            logger.success(f"{self._robot_ip} Arm Connected.")
            return True
        except Exception:
            print(f"connection to {self._robot_ip} error")
            traceback.print_exc()
            raise ConnectionError("Error Connection")

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(self._receiver.getActualQ())

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        if self.servo_running:
            self._stop_servo()
        self._controller.moveJ(target_joint.get_radian_ndarray(),
                               speed=self.joint_speed, acceleration=self.joint_acceleration,
                               asynchronous=asynchronous)
        return True

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        pose = self._receiver.getActualTCPPose()
        return Pose(Coordinate(pose[:3]), Rotation(pose[3:], Rotation.FormType.RPY))

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        if self.servo_running:
            self._stop_servo()
        validity_check = self._controller.isPoseWithinSafetyLimits(pose.get_xyz_m_rpy_radian_ndarray())
        if validity_check:
            self._controller.moveL(pose.get_xyz_m_rpy_radian_ndarray(),
                                   speed=self.tcp_speed, acceleration=self.tcp_acceleration,
                                   asynchronous=asynchronous)
            return True
        else:
            return False

    def sync_target_joint(self, target_joint: np.ndarray):
        self.servo_running = True
        t_start = self._controller.initPeriod()
        self._controller.servoJ(
            target_joint, self.velocity, self.acceleration, self.dt, self.lookahead_time, self.gain
        )
        self._controller.waitPeriod(t_start)

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        self.servo_running = True
        t_start = self._controller.initPeriod()
        self._controller.servoL(
            tool_pose, self.velocity, self.acceleration, self.dt, self.lookahead_time, self.gain
        )
        self._controller.waitPeriod(t_start)

    def _stop_servo(self):
        self._controller.servoStop()
        self.servo_running = False
