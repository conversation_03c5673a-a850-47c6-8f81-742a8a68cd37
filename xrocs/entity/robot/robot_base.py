from abc import ABC, abstractmethod
from typing import Optional, List

import numpy as np

from xrocs.common.data_type import Pose, Joints


class RobotArmDriver(ABC):
    """
    The interface class of a single arm.

    For detailed documents of its methods, please refer to derived classes.

    A "__new__" method is recommended to make sure instantiation happens only once.
    """
    @abstractmethod
    def __init__(self, robot_ip: str) -> None:
        """"""

    ## 1- Must be implemented.
    @abstractmethod
    def num_dofs(self) -> int:
        """Get the number of joints of the robot.

        Returns:
            int: The number of joints of the robot.
        """
        raise NotImplementedError
    
    @abstractmethod
    def connect(self) -> bool:
        """Connect the robot arm.
        
        Returns:
            bool:success or not.
        """

    @abstractmethod
    def get_current_joint(self) -> Optional[Joints]:
        """Get the Joint state.
        
        Return: Current joint values.
        """

    @abstractmethod
    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        """Set moving target.
        """

    @abstractmethod
    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        """Return the effector pose.

        Returns: Pose.
        """

    @abstractmethod
    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        """Move the arms.

        Args:
            pose: single Pose.
            asynchronous: default False.

        Returns:
            The trajectory_state from the response.
        """

    @abstractmethod
    def sync_target_joint(self, target_joint: np.ndarray):
        """Sync the robot to a given joints.

            Args:
                target_joint (np.ndarray): The joint state the leader robot to.
        """

    @abstractmethod
    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        """Sync the robot to a given pose.

            Args:
                tool_pose (tool_pose): The pose to command the leader robot to.
        """
