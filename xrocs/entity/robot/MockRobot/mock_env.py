import numpy as np
from typing import Optional
from xrocs.common.data_type import Joints, Pose, Coordinate, Rotation
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver


class MockRobot(RobotArmDriver):
    """A class representing a Mock robot."""

    def __init__(self, robot_ip: str) -> None:
        self._robot_ip = robot_ip

    def num_dofs(self) -> int:
        return 7

    def connect(self) -> bool:
        logger.success(f"{self._robot_ip} Arm Connected.")
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.zeros(7), num_of_dofs=self.num_dofs())

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        logger.success(f"Mock robot reach target joint: {target_joint}")
        return True

    def sync_target_joint(self, target_joint: np.ndarray):
        ...
        # logger.success(f"Mock robot sync target joint: {target_joint}")

    # Optional
    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        return Pose(Coordinate([0, 0, 0]), Rotation([0, 0, 0]))

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        # logger.success(f"Mock robot reach tool cartesian pose: {pose}")
        return True

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        ...
        # logger.success(f"Mock robot sync tool cartesian pose: {tool_pose}")
