import time
from typing import Optional

import numpy as np
import rospy
import tqdm
from sensor_msgs.msg import JointState
from std_msgs.msg import Header

from xrocs.common.data_type import Pose, Joints
from xrocs.entity.robot.robot_base import RobotArmDriver
from bodyctrl_msgs.msg import MotorStatusMsg, CmdSetMotorPosition, SetMotorPosition


class Tienkung2Robot(RobotArmDriver):
    def __init__(self, robot_ip: str, local_ip: str):
        self.dual_arm_publisher = rospy.Publisher('/joint_states_flex_freq', JointState, queue_size=10)
        self.dual_arm_controller = rospy.Publisher('/arm/cmd_pos', CmdSetMotorPosition, queue_size=10)

        self.left_jpos = None
        self.right_jpos = None

        self.rate = rospy.Rate(400)

    def num_dofs(self) -> int:
        return 14

    def connect(self) -> bool:
        rospy.Subscriber('/arm/status', MotorStatusMsg, self._arm_callback)
        for _ in tqdm.tqdm(range(3), desc="Warm Up Node"):
            time.sleep(1)
        return True

    def _arm_callback(self, data):
        tmp = []
        for val in data.status:
            tmp.append(val.pos)
        self.left_jpos = tmp[:7]
        self.right_jpos = tmp[7:]

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.concatenate([self.left_jpos, self.right_jpos]), num_of_dofs=self.num_dofs())

    @staticmethod
    def _construct_dual_arm_ctrl_msg(target_joint: list[float]):
        msg = CmdSetMotorPosition()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        for idx, val in enumerate(target_joint):
            cmd = SetMotorPosition()
            if idx < 7:
                cmd.name = 11+idx
            else:
                cmd.name = 14+idx
            cmd.pos = val
            cmd.spd = 150
            # if idx == 1 or idx == 8:
            #     cmd.spd = 12.0
            # elif idx == 4 or idx == 11:
            #     cmd.spd = 15.0
            # else:
            #     cmd.spd = 8.0
            cmd.cur = 80.0
            msg.cmds.append(cmd)
        return msg

# 0 1 2 3 4 5 6
# 7 8 9 10 11 12 13

    @staticmethod
    def _construct_dual_arm_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.name = [str(i) for i in range(1, 15)]
        msg.position = target_joint
        msg.velocity = [0] * 14
        return msg

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        fine_step = 500
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 target_joint.get_radian_ndarray(), fine_step)
        for stp in step_array:
            # self.dual_arm_publisher.publish(self._construct_dual_arm_msg(stp))
            self.dual_arm_controller.publish(self._construct_dual_arm_ctrl_msg(stp))
            self.rate.sleep()
        return True

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        return None

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        raise NotImplementedError("Not Implemented: def reach_tool_cartesian_pose()")

    def sync_target_joint(self, target_joint: np.ndarray):
        self.dual_arm_controller.publish(self._construct_dual_arm_ctrl_msg(target_joint.tolist()))
        # self.dual_arm_publisher.publish(self._construct_dual_arm_msg(target_joint.tolist()))

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        raise NotImplementedError("Not Implemented: def sync_tool_cartesian_pose()")
