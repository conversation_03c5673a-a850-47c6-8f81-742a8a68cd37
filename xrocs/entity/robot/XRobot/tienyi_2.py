import json
import threading
import time
from typing import Any, Dict, List, Literal, Optional, Tuple

import numpy as np
import rclpy
from rclpy.node import Node
import tf2_ros
import tqdm
from cm_msgs.srv import CommonStreamRos2
from geometry_msgs.msg import Point
from sensor_msgs.msg import JointState
from std_msgs.msg import Bool, Float64, Header, Int32, String
from std_srvs.srv import SetBool, Empty
from xrocs.common.data_type import Joints, Pose
from xrocs.entity.robot.robot_base import RobotArmDriver
from xrocs.utils.logger.logger_loader import logger


class TienYi2Robot(RobotArmDriver):
    def __init__(self, node: Node, robot_ip: str, local_ip: str) -> None:
        self.node = node
        self.qos_profile = 10
        
        self.dual_arm_publisher = self.node.create_publisher(JointState, '/joint_states_flex_freq', self.qos_profile)
        self.left_arm_publisher = self.node.create_publisher(JointState, '/arm/ctrl_left', self.qos_profile)
        self.right_arm_publisher = self.node.create_publisher(JointState, '/arm/ctrl_right', self.qos_profile)

        # ----
        # MoveIt! Based Control Components.
        # @Young Zheng
        # ----
        # X-Humanoid obstacle perception service (@Rancho He).
        self.x_humanoid_obstacle_map_client = self.node.create_client(SetBool, '/obstacle_map')
        self.x_humanoid_clear_octomap_client = self.node.create_client(Empty, '/clear_octomap')
        self.x_humanoid_surrounding_obstacle_perception_event = threading.Event()
        # X-Humanoid update custom obstacles before reach waypoints.
        self.x_humanoid_waypoints_update_custom_obstacle_client = self.node.create_client(CommonStreamRos2, '/dual_arm_collision_object')
        self.x_humanoid_waypoints_obstacle_names = set()  # Store the names of the obstacles already added.
        # X-Humanoid reach joints with self collision avoidance and/or custom and perceived environment obstacles.
        self.x_humanoid_joints_control_client = self.node.create_client(CommonStreamRos2, '/joint_space_control')
        self.x_humanoid_joints_control_server = self.node.create_service(CommonStreamRos2, '/joint_action_report', self._x_humanoid_joints_control_action_report_callback)
        self.x_humanoid_joints_control_action_completed_event = threading.Event()
        self.x_humanoid_joints_control_task_result = None
        # X-Humanoid reach waypoints service.
        self.x_humanoid_waypoints_client = self.node.create_client(CommonStreamRos2, '/dual_arm_waypoints')
        self.x_humanoid_waypoints_server = self.node.create_service(CommonStreamRos2, '/waypoints_action_report', self._x_humanoid_waypoints_action_report_callback)
        self.x_humanoid_waypoints_left_action_completed_event = threading.Event()
        self.x_humanoid_waypoints_left_task_result = None
        self.x_humanoid_waypoints_left_motion_id = None
        self.x_humanoid_waypoints_right_action_completed_event = threading.Event()
        self.x_humanoid_waypoints_right_task_result = None
        self.x_humanoid_waypoints_right_motion_id = None

        # ----
        # TF2
        # ----
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self.node)



        self.node.create_subscription(JointState, '/joint_state_broadcaster/joint_states', self._arm_joints_callback, self.qos_profile)
        self.arm_joints = []

        self.x_humanoid_current_moveit_service_state = False
        self.x_humanoid_current_qp_service_state = False
        # X-Humanoid control mode switch @Yong Zheng @Nikoo Yang
        self.x_humanoid_moveit_service_mode_client = self.node.create_client(SetBool, '/moveit_controller_enable')
        self.x_humanoid_qp_service_mode_client = self.node.create_client(SetBool, '/qp_controller_enable')
        
        #################################################################################################################################################
        
 
        # X-Humanoid sync reach Cartesian pose service @Nikoo Yang
        self.x_humanoid_sync_reach_waypoints_publisher = self.node.create_publisher(String, '/wbc_absolute_motion', self.qos_profile)
        self.x_humanoid_sync_update_mode_publisher = self.node.create_publisher(Int32, '/wbc_motion_mode', self.qos_profile)
        self.x_humanoid_sync_current_mode = None
        self.x_humanoid_sync_set_time_limit_publisher = self.node.create_publisher(Float64, '/wbc_time_limit', self.qos_profile)
        self.x_humanoid_sync_set_speed_limit_publisher = self.node.create_publisher(Float64, '/wbc_speed_limit', self.qos_profile)
        self.x_humanoid_sync_set_waist_pos_publisher = self.node.create_publisher(Point, '/wbc_waist_pos', self.qos_profile)
        self.x_humanoid_sync_set_ori_weight_publisher = self.node.create_publisher(Point, '/wbc_ori_weight', self.qos_profile)
        self.x_humanoid_sync_reach_waypoints_stop_flag = None
        self.node.create_subscription(Bool, '/wbc_motion_result', self._x_humanoid_sync_reach_waypoints_stop_flag_callback, self.qos_profile)
        self.x_humanoid_sync_joints_publisher = self.node.create_publisher(JointState, '/wbc_joint_pos', self.qos_profile)
 



        self.left_jpos = None
        self.right_jpos = None
        # ROS2建议用定时器替代Rate，若仍需Rate可用rclpy.Rate
        # self.rate = rclpy.Rate(400)
        self.rate_hz = 400

    def num_dofs(self) -> int:
        return 7*2+4

    def connect(self) -> bool:
        for _ in tqdm.tqdm(range(3), desc="Warm Up Node"):
            time.sleep(1)
        return True

    def get_current_joint(self) -> Optional[Joints]:
        raise NotImplementedError
        # return Joints(np.concatenate([self.left_jpos, self.right_jpos]), num_of_dofs=self.num_dofs())
    
    def _arm_joints_callback(self, data):
        first_leg_pitch_joint_value = data.position[data.name.index('first_leg_pitch_joint')]
        second_leg_pitch_joint = data.position[data.name.index('second_leg_pitch_joint')]
        waist_pitch_joint = data.position[data.name.index('waist_pitch_joint')]
        waist_yaw_joint = data.position[data.name.index('waist_yaw_joint')]
        shoulder_pitch_l_joint = data.position[data.name.index('shoulder_pitch_l_joint')]
        shoulder_roll_l_joint = data.position[data.name.index('shoulder_roll_l_joint')]
        shoulder_yaw_l_joint = data.position[data.name.index('shoulder_yaw_l_joint')]
        elbow_pitch_l_joint = data.position[data.name.index('elbow_pitch_l_joint')]
        elbow_yaw_l_joint = data.position[data.name.index('elbow_yaw_l_joint')]
        wrist_pitch_l_joint = data.position[data.name.index('wrist_pitch_l_joint')]
        wrist_roll_l_joint = data.position[data.name.index('wrist_roll_l_joint')]
        shoulder_pitch_r_joint = data.position[data.name.index('shoulder_pitch_r_joint')]
        shoulder_roll_r_joint = data.position[data.name.index('shoulder_roll_r_joint')]
        shoulder_yaw_r_joint = data.position[data.name.index('shoulder_yaw_r_joint')]
        elbow_pitch_r_joint = data.position[data.name.index('elbow_pitch_r_joint')]
        elbow_yaw_r_joint = data.position[data.name.index('elbow_yaw_r_joint')]
        wrist_pitch_r_joint = data.position[data.name.index('wrist_pitch_r_joint')]
        wrist_roll_r_joint = data.position[data.name.index('wrist_roll_r_joint')]

        self.arm_joints = [
            first_leg_pitch_joint_value,
            second_leg_pitch_joint,
            waist_pitch_joint,
            waist_yaw_joint,
            shoulder_pitch_l_joint,
            shoulder_roll_l_joint,
            shoulder_yaw_l_joint,
            elbow_pitch_l_joint,
            elbow_yaw_l_joint,
            wrist_pitch_l_joint,
            wrist_roll_l_joint,
            shoulder_pitch_r_joint,
            shoulder_roll_r_joint,
            shoulder_yaw_r_joint,
            elbow_pitch_r_joint,
            elbow_yaw_r_joint,
            wrist_pitch_r_joint,
            wrist_roll_r_joint
        ]

    def get_current_joints(self):
        return self.arm_joints

    @staticmethod
    def _construct_dual_arm_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        # msg.header.stamp = self.get_clock().now().to_msg()
        msg.name = [str(i) for i in range(1, 15)]
        msg.position = target_joint
        msg.velocity = [0] * 14
        return msg

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        fine_step = 500
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 target_joint.get_radian_ndarray(), fine_step)
        for stp in step_array:
            self.dual_arm_publisher.publish(self._construct_dual_arm_msg(stp))
            time.sleep(1 / self.rate_hz)
        return True

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        return None

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        raise NotImplementedError("Not Implemented: def reach_tool_cartesian_pose()")

    def sync_target_joint(self, target_joint: np.ndarray):
        self.dual_arm_publisher.publish(self._construct_dual_arm_msg(target_joint.tolist()))

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        raise NotImplementedError("Not Implemented: def sync_tool_cartesian_pose()")
    
    def x_humanoid_reach_cartesian_pose(self, frame: Literal['waist_yaw_link', 'left_tcp_link', 'right_tcp_link'] = 'waist_yaw_link', use_cartesian_path: bool = False, left_motion_id: Optional[int] = None, right_motion_id: Optional[int] = None, left_waypoints: List[List[float]] = [], right_waypoints: List[List[float]] = []) -> bool:
        """Send target waypoints to the motion control server (for Young Zheng).
        
        Args:
            frame: The reference frame of the waypoints. 'waist_yaw_link' is for the dual-arm motion at waist frame. 'left_tcp_link' and 'right_tcp_link' are for the single-arm motion at their TCP frames.
            use_cartesian_path: The flag to use Cartesian path (i.e., linear path planning), this is only valid for single-arm motion.
            left_motion_id: The motion ID for the left arm.
            right_motion_id: The motion ID for the right arm.
            left_waypoints: The list of target waypoints for the left arm, for example, [[x, y, z, x, y, z, w], [x, y, z, x, y, z, w],...].
            right_waypoints: The list of target waypoints for the right arm, for example, [[x, y, z, x, y, z, w], [x, y, z, x, y, z, w],...].
        """
        assert left_waypoints or right_waypoints, "At least one arm waypoints should be specified"

        self.x_humanoid_waypoints_left_action_completed_event.clear()
        self.x_humanoid_waypoints_left_task_result = None
        if self.x_humanoid_waypoints_left_motion_id is None:
            self.x_humanoid_waypoints_left_motion_id = left_motion_id

        self.x_humanoid_waypoints_right_action_completed_event.clear()
        self.x_humanoid_waypoints_right_task_result = None
        if self.x_humanoid_waypoints_right_motion_id is None:
            self.x_humanoid_waypoints_right_motion_id = right_motion_id

        req_json_data = {
            'frame': frame,
            'use_cartesian_path': use_cartesian_path,
            'left_motion_id': left_motion_id,
            'right_motion_id': right_motion_id,
            'left_waypoints': left_waypoints,
            'right_waypoints': right_waypoints
        }

        req = CommonStreamRos2.Request()
        req.msg_id = ''
        req.json_data = json.dumps(req_json_data)
        req.data = []

        try:
            response = self.x_humanoid_waypoints_client.call(req)
            if not response.result:
                logger.error("X-Humanoid waypoints service failed.")
                return False
            logger.info("X-Humanoid waypoints service success.")
            return True
        except Exception as e:
            logger.error(f"X-Humanoid waypoints service call failed: {e}")
            return False

    def _x_humanoid_waypoints_action_report_callback(self, request, response):
        """Handle the waypoints action report (for Young Zheng).

        Args:
            request: The request message.
        """
        try:
            report_json_data = json.loads(request.json_data)
            action_name = report_json_data.get("action_name")  # i.e., "motion_result"
            left_motion_id = report_json_data.get("left_motion_id")
            right_motion_id = report_json_data.get("right_motion_id")
            result = report_json_data.get("result")  # Success (1) or Failure (0)

            logger.info(f"Received X-Humanoid waypoints action report: action_name={action_name}, left_motion_id={left_motion_id}, right_motion_id={right_motion_id}, result={result}")

            if left_motion_id is not None:
                self.x_humanoid_waypoints_left_action_completed_event.set()
                self.x_humanoid_waypoints_left_task_result = result

            if right_motion_id is not None:
                self.x_humanoid_waypoints_right_action_completed_event.set()
                self.x_humanoid_waypoints_right_task_result = result

            response.result = True
            print(f"return response to {response}")
            return response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON: {e}.")
            response.result = False
            return response

    def x_humanoid_reach_joints(self, left_target_joints: List[float], right_target_joints: List[float], motion_mode: Literal['dual_arm', 'left_arm', 'right_arm']) -> bool:
        """Send target joints to the motion control server for self /and environment collision avoidance (for Young Zheng).

        Args:
            left_target_joints: The target joint values for the left arm.
            right_target_joints: The target joint values for the right arm.
            motion_mode: The motion mode, currently supports 'dual_arm', 'left_arm', or 'right_arm'.

        Returns:
            True if the request received successfully, False otherwise.
        """
        assert left_target_joints or right_target_joints, "At least one arm target joints should be specified"

        req_json_data = {
            "group": motion_mode,
            "shoulder_pitch_l_joint": left_target_joints[0],
            "shoulder_roll_l_joint": left_target_joints[1],
            "shoulder_yaw_l_joint": left_target_joints[2],
            "elbow_pitch_l_joint": left_target_joints[3],
            "elbow_yaw_l_joint": left_target_joints[4],
            "wrist_pitch_l_joint": left_target_joints[5],
            "wrist_roll_l_joint": left_target_joints[6],
            "shoulder_pitch_r_joint": right_target_joints[0],
            "shoulder_roll_r_joint": right_target_joints[1],
            "shoulder_yaw_r_joint": right_target_joints[2],
            "elbow_pitch_r_joint": right_target_joints[3],
            "elbow_yaw_r_joint": right_target_joints[4],
            "wrist_pitch_r_joint": right_target_joints[5],
            "wrist_roll_r_joint": right_target_joints[6],
        }

        req = CommonStreamRos2.Request()
        req.msg_id = ''
        req.json_data = json.dumps(req_json_data)
        req.data = []

        try:
            response = self.x_humanoid_joints_control_client.call(req)
            if not response.result:
                logger.error("X-Humanoid joints control service failed.")
                return False
            logger.info("X-Humanoid joints control service success.")
            return True
        except Exception as e:
            logger.error(f"X-Humanoid joints control service call failed: {e}")
            return False

    def _x_humanoid_joints_control_action_report_callback(self, request, response) -> CommonStreamRos2.Response:
        """Handle the joints control action report (for Young Zheng)."""
        try:
            report_json_data = json.loads(request.json_data)
            action_name = report_json_data.get("action_name")  # i.e., "motion_result"
            action_result = report_json_data.get("result")  # Success (1) or Failure (0)

            logger.info(f"Received X-Humanoid joints control action report: action_name={action_name}, result={action_result}")

            if action_name == "motion_result":
                self.x_humanoid_joints_control_task_result = action_result
                self.x_humanoid_joints_control_action_completed_event.set()

            response.result = True
            return response

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON: {e}.")
            response.result = False
            return response

    
    def x_humanoid_wait_joints_control_action_finish(self) -> bool:
        """Wait for the X-Humanoid joints control action to finish (for Young Zheng).
                
        Returns:
            True if the action finished successfully, False otherwise.
        """
        if self.x_humanoid_joints_control_action_completed_event.is_set():
            return True
        
        logger.info("Waiting for X-Humanoid joints control action to finish...")
        while not self.x_humanoid_joints_control_action_completed_event.is_set():
            time.sleep(0.1)
        
        result = self.x_humanoid_joints_control_task_result
        logger.info(f"X-Humanoid joints control action finished with result: {result}.")
        
        self.x_humanoid_joints_control_action_completed_event.clear()
        self.x_humanoid_joints_control_task_result = None
        return result == 1

    def x_humanoid_wait_waypoints_action_finish(self) -> bool:
        """Wait for the X-Humanoid waypoints action to finish (for Young Zheng).
                
        Returns:
            True if the action finished successfully, False otherwise.
        """
        if self.x_humanoid_waypoints_left_motion_id is not None:
            logger.info(f"Waiting for X-Humanoid waypoints (Left) action to finish (motion ID {self.x_humanoid_waypoints_left_motion_id})...")
            while not self.x_humanoid_waypoints_left_action_completed_event.is_set():
                time.sleep(0.1)
            logger.info(f"X-Humanoid waypoints (Left) action finished with result: {self.x_humanoid_waypoints_left_task_result}.")

            self.x_humanoid_waypoints_left_action_completed_event.clear()
            self.x_humanoid_waypoints_left_task_result = None
            self.x_humanoid_waypoints_left_motion_id = None

        if self.x_humanoid_waypoints_right_motion_id is not None:
            logger.info(f"Waiting for X-Humanoid waypoints (Right) action to finish (motion ID {self.x_humanoid_waypoints_right_motion_id})...")
            while not self.x_humanoid_waypoints_right_action_completed_event.is_set():
                time.sleep(0.1)
            logger.info(f"X-Humanoid waypoints (Right) action finished with result: {self.x_humanoid_waypoints_right_task_result}.")

            self.x_humanoid_waypoints_right_action_completed_event.clear()
            self.x_humanoid_waypoints_right_task_result = None
            self.x_humanoid_waypoints_right_motion_id = None

        logger.info(f"X-Humanoid waypoints action finished. Current motion ID is L {self.x_humanoid_waypoints_left_motion_id} and R {self.x_humanoid_waypoints_right_motion_id}")

        return self.x_humanoid_waypoints_left_task_result == 1 and self.x_humanoid_waypoints_right_task_result == 1

    def _x_humanoid_waypoints_add_custom_obstacle(self, sharp_type: Literal['BOX', 'SPHERE'], object_name: str, dimensions: List[float], pose: List[float]) -> bool:
        """Add a custom obstacle to the X-Humanoid waypoints service for motion planning (for Young Zheng).
        
        Args:
            sharp_type: The sharp of the obstacle, currently supports 'BOX' or 'SPHERE'.
            object_name: The name of the obstacle.
            dimensions: The dimensions of the obstacle.
            pose: The pose of the obstacle, for example, [x, y, z, x, y, z, w].
        
        Returns:
            True if the obstacle was added successfully, False otherwise.
        """
        req_json_data = {
            'cmd': 'add',
            'sharp_type': sharp_type,
            'object_name': object_name,
            'dimensions': dimensions,
            'pose': pose
        }

        req = CommonStreamRos2.Request()
        req.msg_id = ''
        req.json_data = json.dumps(req_json_data)
        req.data = []

        try:
            response = self.x_humanoid_waypoints_update_custom_obstacle_client.call(req)
            if not response.result:
                logger.error("X-Humanoid waypoints add custom obstacle service failed.")
                return False
            logger.info("X-Humanoid waypoints add custom obstacle service success.")
            self.x_humanoid_waypoints_obstacle_names.add(object_name)
            return True
        except Exception as e:
            logger.error(f"X-Humanoid waypoints add custom obstacle service call failed: {e}")
            return False

    def x_humanoid_waypoints_add_custom_obstacles(self, obstacles: List[Dict[str, Any]]) -> bool:
        """Add multiple custom obstacles to the X-Humanoid waypoints service for motion planning (for Young Zheng).
        
        Args:
            obstacles: A list of obstacles, each obstacle is a dictionary with keys 'sharp_type', 'name', 'dimensions', and 'pose'. For example, [{"sharp_type": "BOX", "name": "obstacle1", "dimensions": [1, 1, 1], "pose": [0, 0, 0, 0, 0, 0, 1]}, ...].
        
        Returns:
            True if all obstacles were added successfully, False otherwise.
        """
        success = True
        for obstacle in obstacles:
            result = self._x_humanoid_waypoints_add_custom_obstacle(**obstacle)
            success &= result
            # time.sleep(0.05)
        return success

    def _x_humanoid_waypoints_remove_custom_obstacle(self, object_name: str) -> bool:
        """Remove a custom obstacle from the X-Humanoid waypoints service for motion planning (for Young Zheng).
        
        Args:
            object_name: The name of the obstacle to remove.
        
        Returns:
            True if the obstacle was removed successfully, False otherwise.
        """
        assert object_name in self.x_humanoid_waypoints_obstacle_names, f"Obstacle {object_name} not found."

        req_json_data = {
            'cmd': 'remove',
            'object_name': object_name
        }

        req = CommonStreamRos2.Request()
        req.msg_id = ''
        req.json_data = json.dumps(req_json_data)
        req.data = []

        try:
            response = self.x_humanoid_waypoints_update_custom_obstacle_client.call(req)
            if not response.result:
                logger.error("X-Humanoid waypoints remove custom obstacle service failed.")
                return False
            logger.info("X-Humanoid waypoints remove custom obstacle service success.")
            self.x_humanoid_waypoints_obstacle_names.discard(object_name)
            return True
        except Exception as e:
            logger.error(f"X-Humanoid waypoints remove custom obstacle service call failed: {e}")
            return False

    def x_humanoid_waypoints_remove_custom_obstacles(self, names: List[str]) -> bool:
        """Remove multiple custom obstacles from the X-Humanoid waypoints service for motion planning (for Young Zheng).
        
        Args:
            names: A list of obstacle names to remove.
        
        Returns:
            True if all obstacles were removed successfully, False otherwise.
        """
        success = True
        for object_name in names:
            result = self._x_humanoid_waypoints_remove_custom_obstacle(object_name)
            success &= result
        return success

    def x_humanoid_waypoints_remove_all_obstacles(self) -> bool:
        """Remove all custom obstacles from the X-Humanoid waypoints service for motion planning (for Young Zheng).
        
        Returns:
            True if all obstacles were removed successfully, False otherwise.
        """
        return self.x_humanoid_waypoints_remove_custom_obstacles(list(self.x_humanoid_waypoints_obstacle_names))
    

    """
    self.x_humanoid_obstacle_map_client = rospy.ServiceProxy('/obstacle_map', SetBool)
    self.x_humanoid_clear_octomap_client = rospy.ServiceProxy('/clear_octomap', Empty)
    self.x_humanoid_surrounding_obstacle_perception_event = threading.Event()
    """


    def x_humanoid_enable_surrounding_obstacle_perception(self) -> bool:
        """Enable surrounding obstacle perception service (for Rancho He).
        
        That is, take a snapshot of the surrounding environment then generate octomap for MoveIt! planning.
        """
        if self.x_humanoid_surrounding_obstacle_perception_event.is_set():
            logger.info("Surrounding obstacle perception is already enabled.")
            return True

        try:
            req = SetBool.Request()
            req.data = True
            response = self.x_humanoid_obstacle_map_client.call(req)
            if not response.success:
                logger.error(f"X-Humanoid obstacle perception service call failed. response is {response}.")
                return False
            logger.info(f"X-Humanoid obstacle perception service call success. response is {response}.")
            self.x_humanoid_surrounding_obstacle_perception_event.set()
            return True
        except Exception as e:
            logger.error(f"X-Humanoid obstacle perception service call failed: {e}")
            return False

    def x_humanoid_disable_surrounding_obstacle_perception(self) -> bool:
        """Disable surrounding obstacle perception service (for Rancho He)."""
        if not self.x_humanoid_surrounding_obstacle_perception_event.is_set():
            logger.info("Surrounding obstacle perception is already disabled.")
            return True

        try:
            empty_req = Empty.Request()
            self.x_humanoid_clear_octomap_client.call(empty_req)

        except Exception as e:
            logger.error(f"X-Humanoid clear octomap service call failed: {e}")
            return False

        try:
            req = SetBool.Request()
            req.data = False
            response = self.x_humanoid_obstacle_map_client.call(req)
            if not response.success:
                logger.error("X-Humanoid obstacle perception service call failed.")
                return False
            logger.info("X-Humanoid obstacle perception service call success.")
            self.x_humanoid_surrounding_obstacle_perception_event.clear()
            return True
        except Exception as e:
            logger.error(f"X-Humanoid obstacle perception service call failed: {e}")
            return False

    def x_humanoid_ik(self, left_pose: List[float] = [], right_pose: List[float] = []) -> Tuple[List[float], List[float]]:
        """X-Humanoid IK service (for Rancho He).
        
        Args:
            left_pose: The target pose for the left arm, for example, [x, y, z, x, y, z, w].
            right_pose: The target pose for the right arm, for example, [x, y, z, x, y, z, w].
        
        Returns:
            The computed joint angles for the left and right arms.
        """
        assert left_pose or right_pose, "At least one of left_pose or right_pose must be provided."

        req = CommonStreamRos2.Request()
        req.left_pose = left_pose
        req.right_pose = right_pose
        try:
            response = self.x_humanoid_ik_client.call(req)
            left_joint_angles = response.left_joint_angles
            right_joint_angles = response.right_joint_angles

            if (left_pose and not left_joint_angles) or (right_pose and not right_joint_angles):
                logger.error("X-Humanoid IK failed.")
                return [], []
            logger.info(f"X-Humanoid IK service call success. Left joint angles: {left_joint_angles}, right joint angles: {right_joint_angles}.")
            return left_joint_angles, right_joint_angles
        except Exception as e:
            logger.error(f"X-Humanoid IK service call failed: {e}")
            return [], []

    def x_humanoid_sync_reach_cartesian_pose(self, left_waypoints: List[List[float]] = [], right_waypoints: List[List[float]] = []):
        """WBC for X-Humanoid real time (for Nikoo Yang).

        Publish left and right arm waypoints to the WBC node in the servo mode (400 Hz).
        
        Args:
            left_waypoints: A list of left arm waypoints, each waypoint is a list of [x, y, z, x, y, z, w].
            right_waypoints: A list of right arm waypoints, each waypoint is a list of [x, y, z, x, y, z, w].
        """
        assert left_waypoints or right_waypoints, "At least one of left_waypoints or right_waypoints must be provided."


        # TODO Based on the guozhen's requirements

        json_data = {
            'left_waypoints': left_waypoints,
            'right_waypoints': right_waypoints
        }

        msg = String()
        msg.data = json.dumps(json_data)
        self.x_humanoid_sync_reach_waypoints_publisher.publish(msg)
        logger.info(f"X-Humanoid sync publish {msg.data}")

    def _x_humanoid_sync_reach_waypoints_stop_flag_callback(self, msg):
        self.x_humanoid_sync_reach_waypoints_stop_flag = msg.data

    def x_humanoid_sync_reach_cartesian_pose_wait_until_stop(self):
        self.x_humanoid_sync_reach_waypoints_stop_flag = False

        while not self.x_humanoid_sync_reach_waypoints_stop_flag:
            time.sleep(0.1)

    def x_humanoid_sync_update_mode(self, mode: Literal[0, 1, 2, 3] = 3) -> None:
        """Update the motion mode for X-Humanoid sync reach Cartesian pose service (for Nikoo Yang).
        
        Args:
            mode: The motion mode to update.
            - 0: 只有腰动
            - 1: 只有腿动
            - 2: 都不动
            - 3: 都动（默认）
        """

        msg = Int32()
        msg.data = mode
        self.x_humanoid_sync_update_mode_publisher.publish(msg)
        logger.info(f"X-Humanoid sync update mode to {mode}.")
        self.x_humanoid_sync_current_mode = mode

    def x_humanoid_sync_set_time_limit(self, time_limit: float = 8.0) -> None:
        """Set the timeout for X-Humanoid sync reach Cartesian pose service (for Nikoo Yang).
        
        Args:
            time_limit: The timeout in seconds, default is 8.0 seconds.
        """
        assert time_limit > 0, "Time limit must be positive."

        msg = Float64()
        msg.data = time_limit
        self.x_humanoid_sync_set_time_limit_publisher.publish(msg)
        logger.info(f"X-Humanoid sync set time limit to {time_limit}.")

    def x_humanoid_sync_set_speed_limit(self, speed_limit: float = 1.0) -> None:
        """Set the speed limit for X-Humanoid sync reach Cartesian pose service (for Nikoo Yang).
        
        Args:
            speed_limit: The speed limit, of range (0., 3.14), in unit rad/s, only for the arm speed.
        """
        assert speed_limit > 0, "Speed limit must be positive."

        msg = Float64()
        msg.data = speed_limit
        self.x_humanoid_sync_set_speed_limit_publisher.publish(msg)
        logger.info(f"X-Humanoid sync set speed limit to {speed_limit}.")

    def x_humanoid_sync_set_waist_pos(self, waist_pos: Tuple[float, float, float]) -> None:
        """Set the waist poss (i.e., x, y, and z) for X-Humanoid sync reach Cartesian pose service (for Nikoo Yang).
        
        Args:
            waist_pos: The waist position, a tuple of (x, y, z). Currently, the y is invalid.
        """
        assert self.x_humanoid_sync_current_mode == 3, "Current mode is not 3, please update mode first."

        msg = Point()
        msg.x = waist_pos[0]
        msg.y = 0.
        msg.z = waist_pos[2]

        self.x_humanoid_sync_set_waist_pos_publisher.publish(msg)
        logger.info(f"X-Humanoid sync set waist position to {waist_pos}.")

    def x_humanoid_sync_set_ori_weight(self, ori_weight: Tuple[float, float, float]) -> None:
        """Set the orientation weight for X-Humanoid sync reach Cartesian pose service (for Nikoo Yang).

        Args:
            ori_weight: The orientation weight at the base frame, a tuple of (x, y, z), in range (0, 1).
        """
        assert 0 < ori_weight[0] < 1 and 0 < ori_weight[1] < 1 and 0 < ori_weight[2] < 1, "Orientation weight must be in range (0, 1)."

        msg = Point()
        msg.x = ori_weight[0]
        msg.y = ori_weight[1]
        msg.z = ori_weight[2]
        self.x_humanoid_sync_set_ori_weight_publisher.publish(msg)
        logger.info(f"X-Humanoid sync set orientation weight to {ori_weight}.")

    def x_humanoid_sync_joints(self, target_joint: List[float]):
        """Publish the joints position for X-Humanoid sync reaching.
        
        Args:
            target_joint: The target joint positions, in the order of:
                - first_leg_pitch_joint
                - second_leg_pitch_joint
                - waist_pitch_joint
                - waist_yaw_joint
                - shoulder_pitch_l_joint
                - shoulder_roll_l_joint
                - shoulder_yaw_l_joint
                - elbow_pitch_l_joint
                - elbow_yaw_l_joint
                - wrist_pitch_l_joint
                - wrist_roll_l_joint
                - shoulder_pitch_r_joint
                - shoulder_roll_r_joint
                - shoulder_yaw_r_joint
                - elbow_pitch_r_joint
                - elbow_yaw_r_joint
                - wrist_pitch_r_joint
                - wrist_roll_r_joint
        """
        msg = JointState()
        msg.header = Header()
        # msg.header.stamp = self.get_clock().now().to_msg()
        msg.name = [
            "first_leg_pitch_joint",
            "second_leg_pitch_joint",
            "waist_pitch_joint",
            "waist_yaw_joint",
            "shoulder_pitch_l_joint",
            "shoulder_roll_l_joint",
            "shoulder_yaw_l_joint",
            "elbow_pitch_l_joint",
            "elbow_yaw_l_joint",
            "wrist_pitch_l_joint",
            "wrist_roll_l_joint",
            "shoulder_pitch_r_joint",
            "shoulder_roll_r_joint",
            "shoulder_yaw_r_joint",
            "elbow_pitch_r_joint",
            "elbow_yaw_r_joint",
            "wrist_pitch_r_joint",
            "wrist_roll_r_joint"
        ]
        msg.position = target_joint
        self.x_humanoid_sync_joints_publisher.publish(msg)
        logger.info(f"X-Humanoid sync set joint position to {target_joint}.")

    def x_humanoid_set_service_mode(self, mode: Literal["moveit", "qp"], flag: bool) -> bool:
        """Set the Tienkung service mode.

        Args:
            mode: The service mode to set.
            flag: The flag to set.

        Returns:
            True if the service mode is set successfully, False otherwise.
        """
        def set_moveit_service_mode(flag: bool) -> bool:
            req = SetBool.Request()
            req.data = flag
            try:
                response = self.x_humanoid_moveit_service_mode_client.call(req)
                if not response.success:
                    logger.error(f"X-Humanoid set moveit service mode failed. response is {response}.")
                    return False
                logger.info(f"X-Humanoid set moveit service mode success. response is {response}.")
                return True
            except Exception as e:
                logger.error(f"X-Humanoid set moveit service mode failed: {e}")
                return False
            
        def set_qp_service_mode(flag: bool) -> bool:
            req = SetBool.Request()
            req.data = flag
            try:
                response = self.x_humanoid_qp_service_mode_client.call(req)
                if not response.success:
                    logger.error(f"X-Humanoid set qp service mode failed. response is {response}.")
                    return False
                logger.info(f"X-Humanoid set qp service mode success. response is {response}.")
                return True
            except Exception as e:
                logger.error(f"X-Humanoid set qp service mode failed: {e}")
                return False
        
        # Make sure only one service mode is enabled at a time, or disable both.
        if self.x_humanoid_current_moveit_service_state + self.x_humanoid_current_qp_service_state > 1:
            result = set_moveit_service_mode(False)
            if result:
                self.x_humanoid_current_moveit_service_state = False

            result = set_qp_service_mode(False)
            if result:
                self.x_humanoid_current_qp_service_state = False

        if mode == "moveit":
            if flag == self.x_humanoid_current_moveit_service_state:
                logger.info(f"X-Humanoid moveit service mode already set to {flag}")
                return True
            else:
                if flag == False:
                    result = set_moveit_service_mode(False)
                    if result:
                        self.x_humanoid_current_moveit_service_state = False

                if flag == True:
                    if self.x_humanoid_current_qp_service_state:
                        result_qp = set_qp_service_mode(False)
                        if result_qp:
                            self.x_humanoid_current_qp_service_state = False

                    result_moveit = set_moveit_service_mode(True)
                    if result_moveit:
                        self.x_humanoid_current_moveit_service_state = True

                    return result_moveit

        elif mode == "qp":
            if flag == self.x_humanoid_current_qp_service_state:
                logger.info(f"X-Humanoid qp service mode already set to {flag}")
                return True
            else:

                if flag == False:
                    result = set_qp_service_mode(False)
                    if result:
                        self.x_humanoid_current_qp_service_state = False

                if flag == True ==  self.x_humanoid_current_moveit_service_state:
                    result_moveit = set_moveit_service_mode(False)
                    if result_moveit:
                        self.x_humanoid_current_moveit_service_state = False

                    result_qp = set_qp_service_mode(True)
                    if result_qp:
                        self.x_humanoid_current_qp_service_state = True

                    return result_qp
                
    def x_humanoid_left_tcp_pose(self) -> Tuple[List[float], List[float]]:
        """
        Get the left TCP pose in the waist yaw frame.

        Returns:
            The left TCP pose in the waist yaw frame.
        """
        return self.x_humanoid_get_trans_bewteen_frames('waist_yaw_link', 'left_tcp_link')

    def x_humanoid_right_tcp_pose(self) -> Tuple[List[float], List[float]]:
        """
        Get the right TCP pose in the waist yaw frame.

        Returns:
            The right TCP pose in the waist yaw frame.
        """
        return self.x_humanoid_get_trans_bewteen_frames('waist_yaw_link', 'right_tcp_link')
    
    def x_humanoid_get_trans_bewteen_frames(self, frame_1_name: str, frame_2_name: str) -> Tuple[List[float], List[float]]:
        """
        Get the transformation between two frames.

        Args:
            frame_1_name: The name of the first frame.
            frame_2_name: The name of the second frame.

        Returns:
            The transformation between the two frames.
        """
        # Get the current time
        now = self.node.get_clock().now()

        # Wait for transform to be available
        if not self.tf_buffer.can_transform(
            frame_1_name,
            frame_2_name,
            now,
            timeout=rclpy.duration.Duration(seconds=10.0)
        ):
            raise RuntimeError("Transform between waist_yaw_link and left_tcp_link not available")

        # Get the transform
        transform = self.tf_buffer.lookup_transform(
            frame_1_name,
            frame_2_name,
            now
        )

        # Extract translation and rotation
        trans = [
            transform.transform.translation.x,
            transform.transform.translation.y,
            transform.transform.translation.z
        ]

        rot = [
            transform.transform.rotation.x,
            transform.transform.rotation.y,
            transform.transform.rotation.z,
            transform.transform.rotation.w
        ]

        return trans, rot
