import time
from typing import Optional
import numpy as np
import rospy
import tqdm
import threading
import time
import queue

from sensor_msgs.msg import JointState
from std_msgs.msg import Header
from xrocs.common.data_type import Pose, Joints
from xrocs.entity.robot.robot_base import RobotArmDriver
from xrocs.utils.motion_interpolator import MotionInterpolator



class TienkungRobot(RobotArmDriver):
    def __init__(self, robot_ip: str, local_ip: str, is_intrp=False):
        self.dual_arm_publisher = rospy.Publisher('/joint_states_flex_freq', JointState, queue_size=1)

        self.left_jpos = None
        self.right_jpos = None

        self.rate = rospy.Rate(400)

        # Interpolator
        self.is_intrp = is_intrp
        if self.is_intrp:
            self.inter_freq = 100
            self.running = True
            self.interpolator = MotionInterpolator(dof=self.num_dofs(), freq=self.inter_freq)
            self.interpolator.start()
            self.output_thread = threading.Thread(target=self._output_worker)
            self.output_thread.start()

    def num_dofs(self) -> int:
        return 14

    def connect(self) -> bool:
        rospy.Subscriber('/human_arm_state_left', JointState, self._left_arm_callback)
        rospy.Subscriber('/human_arm_state_right', JointState, self._right_arm_callback)
        for _ in tqdm.tqdm(range(3), desc="Warm Up Node"):
            time.sleep(1)
        return True

    def _left_arm_callback(self, data):
        self.left_jpos = list(data.position)

    def _right_arm_callback(self, data):
        self.right_jpos = list(data.position)

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.concatenate([self.left_jpos, self.right_jpos]), num_of_dofs=self.num_dofs())

    @staticmethod
    def _construct_dual_arm_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.name = [str(i) for i in range(1, 15)]
        msg.position = target_joint
        msg.velocity = [0] * 14
        return msg
    
    @staticmethod
    def _construct_sg_arm_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.name = [str(i) for i in range(1, 8)]
        msg.position = target_joint
        msg.velocity = [0] * 7
        return msg

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        fine_step = 500
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 target_joint.get_radian_ndarray(), fine_step)
        for stp in step_array:
            self.dual_arm_publisher.publish(self._construct_dual_arm_msg(stp))
            # self.left_arm_publisher.publish(self._construct_sg_arm_msg(stp[0:7].tolist()))
            # self.right_arm_publisher.publish(self._construct_sg_arm_msg(stp[7:14].tolist()))
            self.rate.sleep()
        return True

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        return None

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        raise NotImplementedError("Not Implemented: def reach_tool_cartesian_pose()")

    def sync_target_joint(self, target_joint: np.ndarray):
        if self.is_intrp:
            self.interpolator.add_waypoint(target_joint)
        else:
            self.dual_arm_publisher.publish(self._construct_dual_arm_msg(target_joint.tolist()))
        # self.left_arm_publisher.publish(self._construct_sg_arm_msg(target_joint[0:7].tolist()))
        # self.right_arm_publisher.publish(self._construct_sg_arm_msg(target_joint[7:14].tolist()))

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        raise NotImplementedError("Not Implemented: def sync_tool_cartesian_pose()")
    
    # Interpolator Thread
    def _output_worker(self):
        interval = 1/self.inter_freq  # 100Hz
        next_time = time.time()

        while self.running:
            try:
                point = self.interpolator.output_queue.get(timeout=0.001)
                self.interpolator.last_output = point
            except queue.Empty:
                point = self.interpolator.last_output

            self.dual_arm_publisher.publish(self._construct_dual_arm_msg(point.tolist()))

            next_time += interval
            sleep_time = next_time - time.time()
            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                next_time = time.time()

    def __del__(self):
        self.running = False
        self.output_thread.join()

