import traceback
import numpy as np
import zerorpc
from typing import Optional
from xrocs.common.data_type import Joints, Pose, Coordinate, Rotation
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver


class FrankaRobot(RobotArmDriver):
    def __init__(self, robot_ip: str, arm_type ='left') -> None:
        self._robot_ip = robot_ip
        self._arm_type = arm_type
        self._robot = None

    def num_dofs(self) -> int:
        return 8

    def connect(self) -> bool:
        try:
            self._robot = zerorpc.Client(heartbeat=20)
            port = 4243 if self._arm_type == 'right' else 4242
            self._robot.connect("tcp://" + self._robot_ip + f":{port}")
            logger.success(f"Connected to Franka robot {self._robot_ip}")

            self._robot.launch_controller()
            self._robot.launch_robot()
            logger.success(f'Successfully Robot Launched')
            return True
        except Exception:
            print(f"connection to {self._robot_ip} error")
            traceback.print_exc()
            raise ConnectionError("Error Connection")

    def get_current_joint(self) -> Optional[Joints]:
        robot_state, _ = self._robot.get_robot_state()
        joint_positions = np.array(robot_state['joint_positions'])
        gripper_position = np.array(robot_state['gripper_position'])
        return Joints(np.concatenate([joint_positions, [gripper_position]]), num_of_dofs=self.num_dofs())

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        self._robot.update_joints(target_joint.get_radian_ndarray().tolist(), False, True, None)
        return True

    def sync_target_joint(self, target_joint: np.ndarray):
        self._robot.update_command(target_joint.tolist(), 'joint_position', 'gripper_position', False)

    # Optional
    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        ee_pose = self._robot.get_ee_pose()
        return Pose(Coordinate(ee_pose[:3]), Rotation(ee_pose[3:], Rotation.FormType.RPY))

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        self._robot.update_pose(pose.get_xyz_m_rpy_radian_ndarray().tolist(), False, not asynchronous)
        return True

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        self._robot.update_command(tool_pose.tolist(), 'cartesian_position', None, False)

    def get_driver(self):
        return self._robot
