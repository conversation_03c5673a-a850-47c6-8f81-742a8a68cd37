from xrocs.entity.robot.Franka.franka import <PERSON><PERSON><PERSON><PERSON><PERSON>
from xrocs.common.data_type import Joints


if __name__ == '__main__':
    control_ip = '127.0.0.1'
    franka_right = <PERSON><PERSON><PERSON><PERSON><PERSON>(robot_ip=control_ip, arm_type='right')
    franka_right.connect()
    print(franka_right.get_current_joint())
    home_joint = [0.0, -0.7854, 0.0, -1.5708, 0.0, 1.5708, 0]
    _joint = Joints(home_joint, num_of_dofs=7)
    franka_right.reach_target_joint(_joint)

    franka_left = <PERSON><PERSON><PERSON><PERSON><PERSON>(robot_ip=control_ip, arm_type='left')
    franka_left.connect()
    home_joint = [0.0, -0.7854, 0.0, -1.5708, 0.0, 1.5708, 0]
    print(franka_left.get_current_joint())
    _joint = Joints(home_joint, num_of_dofs=7)
    franka_left.reach_target_joint(_joint)
    print(franka_left.get_tool_cartesian_pose())