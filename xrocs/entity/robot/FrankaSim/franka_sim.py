import traceback
import time
import numpy as np
import zerorpc
from typing import Optional
from xrocs.common.data_type import Joints, Pose, Coordinate, Rotation
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver


class FrankaSimRobot(RobotArmDriver):
    """A class representing a UR robot."""

    def __init__(self, robot_ip: str, arm_type ='left') -> None:
        self._robot_ip = robot_ip
        self._arm_type = arm_type
        self._robot = None

    def num_dofs(self) -> int:
        return 7

    def connect(self) -> bool:
        try:
            self._robot = zerorpc.Client(heartbeat=20, timeout=2)
            self._robot.connect("tcp://" + self._robot_ip + f":4245")
            self._robot.start()
            time.sleep(3)
            logger.success(f"Connected to Franka robot {self._robot_ip}")
            return True
        except Exception:
            print(f"connection to {self._robot_ip} error")
            traceback.print_exc()
            raise ConnectionError("Error Connection")

    def _get_current_joint(self) -> Optional[Joints]:
        all_joints = self._robot.get_all_joints_state()['arm_joint']
        return Joints(all_joints, num_of_dofs=self.num_dofs())

    def get_current_joint(self) -> Optional[Joints]:
        start_time = time.time()
        timeout = 10
        while time.time() - start_time < timeout:
            try:
                return self._get_current_joint()
            except TypeError as e:
                logger.error("Encountered error:", e, "— retrying after delay.")
                time.sleep(0.1)  

        raise TimeoutError("Failed to read images within the allowed time frame.")

    def reach_target_joint(self, target_joint: Joints, asynchronous: bool = False) -> bool:
        joints = target_joint.get_radian_ndarray().tolist()
        self._robot.reach_arm_joints(joints)
        return True

    def sync_target_joint(self, target_joint: np.ndarray):
        self._robot.set_arm_joint(target_joint.tolist())

    # Optional
    def _get_tool_cartesian_pose(self) -> Optional[Pose]:
        ee_pose = self._robot.get_all_joints_state()['ee_pose']
        if ee_pose is None:
            raise TypeError("None Type")
        return Pose(Coordinate(ee_pose[:3]), Rotation(ee_pose[3:], Rotation.FormType.XYZW))

    def get_tool_cartesian_pose(self) -> Optional[Pose]:
        start_time = time.time()
        timeout = 10
        while time.time() - start_time < timeout:
            try:
                return self._get_tool_cartesian_pose()
            except TypeError as e:
                print("Encountered error:", e, "— retrying after delay.")
                time.sleep(0.1)  

        raise TimeoutError("Failed to read images within the allowed time frame.")

    def reach_tool_cartesian_pose(self, pose: Pose, asynchronous: bool = False) -> bool:
        self._robot.update_pose(pose.get_xyz_m_rpy_radian_ndarray().tolist(), False, not asynchronous)
        return True

    def sync_tool_cartesian_pose(self, tool_pose: np.ndarray):
        self._robot.update_command(tool_pose.tolist(), 'cartesian_position', None, False)

    def get_driver(self):
        return self._robot
    
    def reset(self):
        self._robot.start()
    
    def get_all_joints_state(self):
        return self._robot.get_all_joints_state()
    
    def close(self):
        logger.info('waiting for closing...')
        try:
            self._robot.shut_down()
        except zerorpc.exceptions.TimeoutExpired as e:
            logger.success('close isaac done')
            # logger.error(traceback.format_exc())