from typing import Optional, Union

import numpy as np

from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver


class RobotiqGripperFranka(HandDriver):
    def __init__(self, robot_driver):
        self._gripper = robot_driver

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        return True
    
    def open(self) -> bool:
        self.sync_target_joint([0])
        return True

    def close(self) -> bool:
        self.sync_target_joint([1])
        return True

    def get_current_joint(self) -> Optional[Joints]:
        gripper_pos = self._gripper.get_driver().get_gripper_position()
        return Joints(np.array([gripper_pos]), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: Union[np.array, np.float64]):
        if not isinstance(target_joint, (np.float64, np.float32, int)):
            target_joint = target_joint[0]
        target_joint = np.float64(target_joint)
        assert 0 <= target_joint <= 1, "Gripper control parameter must be between 0 and 1"
        self._gripper.get_driver().update_gripper(target_joint, False, True)
        return True