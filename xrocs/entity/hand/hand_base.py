from abc import ABC, abstractmethod
from typing import Op<PERSON>, <PERSON><PERSON>, Dict

import numpy as np

from xrocs.common.data_type import Joints, JointsArray


class HandDriver(ABC):

    @abstractmethod
    def num_dofs(self) -> int:
        """Get the number of joints of the hand.

        Returns:
            int: The number of joints of the hand.
        """
        ...

    @abstractmethod
    def connect(self) -> bool:
        """Connect the hand.

        Returns:
            bool: success or not.
        """
        ...

    @abstractmethod
    def get_current_joint(self) -> Optional[Joints]:
        """Get the joint state.

        Returns:
            Joints: Current joint values.
        """
        ...

    @abstractmethod
    def set_target_joint(self, target_joint: Joints) -> bool:
        """Set moving target.

        Args:
            target_joint: The target joint values.

        Returns:
            bool: success or not.
        """
        ...

    @abstractmethod
    def sync_target_joint(self, target_joint: np.ndarray):
        """Set moving target.

        Args:
            target_joint: The target joint values.

        Returns:
            bool: success or not.
        """
        ...

    @abstractmethod
    def open(self):
        ...

    def close(self):
        ...


class HandInterface:
    """The interface class of mobile robot.

    For detailed documents of its methods, please refer to derived classes.
    """
    # ----
    # <PERSON>ripper
    @abstractmethod
    def help(self) -> None:
        """
        Display the supported configuration (Including custom config).
        Display the supported moving mode.
        Display the supported ctrl mode.
        """
        ...

    @abstractmethod
    def init_comm(self) -> bool :
        """
        Init the communation, connect the hand, check whether connection failure.
        """
        ...

    @abstractmethod
    def load_config(self, toml_file_addr: str) -> bool :
        """
        Load the config file. 
        Using the config file to figure out different devices, different web env.
        """
        ...
    
    # @abstractmethod
    # def get_device_info(self) -> Optional[HandInfo]:
    #     """
    #     Get the serial number, the web info et al..
    #     """
    #     ...
    
    # @abstractmethod
    # def get_joints_defined_info(self) -> JointsInfo :
    #     """
    #     Get the information of the joints defination.
    #     """

    @abstractmethod
    def enable(self) -> bool:
        """
        Enable the hand.
        The status changed from "online" to "controllable".
        """
        ...
    
    @abstractmethod
    def disable(self) -> bool:
        """
        Disable the hand.
        """
        ...

    # @abstractmethod
    # def get_joints_limit(self) -> LimitInfo:
    #     """
    #     Check the joint space limit values, with the unit being degrees.
    #     Simultaneously output the current position.
    #     """
    #     ...

    @abstractmethod
    def get_joints_position(self) -> JointsArray:
        """
        Get the position of each joint.
        """
    
    # @abstractmethod
    # def get_joints_torque(self) -> TorqueArray:
    #     """
    #     Get the joint torque.
    #     """
    
    @abstractmethod
    def set_kinematic_config(self, k_code: int) -> Tuple[bool, Optional[str]]:
        """
        Set the kinematic configuration. (DoFs)
        If input wrong code, return supported configuration list.
        Args:
            k_code = 
                1  : single-DoF mode
                2  : dexterous mode
                11 : custom mode 1   # Customized moving. e.g. 19DoFs --> 6DoFs
                12 : custom mode 2
                ...
        """
        ...
    
    @abstractmethod
    def get_kinematic_info(self) -> Tuple[bool, Optional[str]]:
        """
        Get the current kinematic mode.
        """
    
    @abstractmethod
    def set_ctrl_mode(self, 
                      c_code: int,
                      motor_id: Optional[int]) -> Tuple[bool, Optional[str]]:
        """
        Set the control mode.
        If input wrong code, return supported control mode list.
        Args:
            c_code = 
                1  : all position mode (To target position)
                2  : single motor position mode (motor id required)
                3  : force mode (To target torque)
                4  : single motor force mode (motor id required)
                11 : custom mode 1  
                12 : custom mode 2
                ...
        """
        ...
    
    @abstractmethod
    def get_ctrl_mode(self) -> Optional[int]:
        """
        Get the current control mode.
        """
    
    # @abstractmethod
    # def set_ctrl_target(self, 
    #                     c_code: int, 
    #                     target_param: Optional[JointsArray, TorqueArray]) -> Tuple[bool, Optional[str]]:
    #     """
    #     Set target value.
    #     The length of the target_param has to be checked.
    #     The control_code has to be checked.
    #     """
    #     ...
    
    # @abstractmethod
    # def set_single_ctrl_target(self,
    #                            c_code: int,
    #                            target_param: Optional[Joints, Torque]) -> Tuple[bool, Optional[str]]:
    #     """
    #     Set target value for one motor.
    #     The length of the target_param has to be checked.
    #     The control_code has to be checked.
    #     """
    #     ...

    @abstractmethod
    def go(self) -> Tuple[bool, Optional[str]] :
        """
        Excute the target.
        The excuting process has to run in a seperate thread.
        The main thread must provide the status checking function all the time.
        """
        ...
    
    # @abstractmethod
    # def move_and_wait_for_complete(self,
    #                            c_code: int,
    #                            target_param: Optional[Joints, Torque]) -> Tuple[bool, Optional[str]]:
    #     """
    #     Integrated moving method.
    #     Recommand using 'set_single_ctrl_target' and 'go' method.
    #     """
    
    @abstractmethod
    def stop_motion(self) -> Tuple[bool, Optional[str]]:
        """
        Stop the current motion.
        """
    
    @abstractmethod
    def get_status(self) -> int:
        """Get the current status. 

        Args:
            None
        Returns:
            h_status = 
                0  : hand offline
                1  : online, but not enabled
                2  : enabled, wait for commands
                3  : in moving process
                99 : hand error
        """
        ...

    @abstractmethod
    def get_err_info(self) -> Optional[str]:
        """
        Get error info.
        """
        ...
    
    @abstractmethod
    def clear_err(self) -> Optional[bool]:
        """
        Clear error info.
        """
        ...

    @abstractmethod
    def goto_preconfigured_state(self,
                                 num: int) -> Tuple[bool, Optional[str]]:
        """
        Go to the pre-configured state.
        This pre-configured state is defined by a toml-config file.
        e.g. the clenched State, extended state, 'OK' state.
        """
    
    @abstractmethod
    def goto_home(self) -> Tuple[bool, Optional[str]]:
        """
        Go to home state.
        """
    
    @abstractmethod
    def get_preconfigured_list(self) -> Dict[str, JointsArray]:
        """
        Get the preconfigured list.
        """
    
    @abstractmethod
    def destroy(self) -> None:
        """Destroy the robot.

        Frees resources used by the robot.
        """
        ...

    @abstractmethod
    def manual_calibrate(self) -> Optional[str]:
        """
        TBC
        """

    @abstractmethod
    def get_sensor_data(self) -> Optional[str]:
        """
        TBC
        """