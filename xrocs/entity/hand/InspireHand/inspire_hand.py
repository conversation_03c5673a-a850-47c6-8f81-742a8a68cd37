import time
from typing import Optional
import numpy as np
import rospy
import tqdm
from sensor_msgs.msg import JointState
from std_msgs.msg import Header
from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver


class InspiredHand(HandDriver):
    def __init__(self, hand_ip: str, local_ip: str, hand_type: str):
        self._hand_type = hand_type

        self._hand_publisher = rospy.Publisher(f'/inspire_hand/ctrl/{hand_type}_hand', JointState, queue_size=10)

        self.hand_joint = None

    def num_dofs(self) -> int:
        return 6

    def _hand_callback(self, data):
        self.hand_joint = list(data.position)

    def connect(self) -> bool:
        rospy.Subscriber(f'/inspire_hand/state/{self._hand_type}_hand', JointState, self._hand_callback)
        for _ in tqdm.tqdm(range(1), desc="Warm Up Node"):
            time.sleep(1)
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(self.hand_joint, num_of_dofs=self.num_dofs())

    @staticmethod
    def construct_hand_msg(target_joint: list[float]):
        msg = JointState()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.name = ['1', '2', '3', '4', '5', '6']
        msg.position = target_joint
        # logger.info(f'Published hand: {np.round(jpos, 3).tolist()}')
        return msg

    def set_target_joint(self, target_joint: Joints) -> bool:
        if len(target_joint) == 1:
            val = [target_joint.get_radian_ndarray()[0]]*self.num_dofs()
            val[-1] = 0
            target_joint = Joints(val, num_of_dofs=self.num_dofs())
        self.sync_target_joint(target_joint.get_radian_ndarray())
        return True

    def sync_target_joint(self, target_joint: np.ndarray):
        if len(target_joint) == 1:
            target_joint = np.asarray([target_joint[0]]*self.num_dofs())
        target_joint[-1] = 0
        self._hand_publisher.publish(self.construct_hand_msg(target_joint.tolist()))

    def open(self):
        self.sync_target_joint(np.asarray([1, 1, 1, 1, 1, 0]))
