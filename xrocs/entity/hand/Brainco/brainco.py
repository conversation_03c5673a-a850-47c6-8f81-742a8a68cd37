import time
from typing import Optional
import numpy as np
import rospy
from std_msgs.msg import UInt8MultiArray
from control_brainco_hand.msg import hand_msgs
from control_brainco_hand.srv import control_hand, control_handRequest
from control_brainco_hand.msg import TouchSensor
from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver


class BrainCo(HandDriver):
    def __init__(self, hand_ip: str, local_ip: str):
        rospy.Subscriber('hand_joint_states', hand_msgs, self._hand_joint_callback)
        rospy.Subscriber('hand_status', UInt8MultiArray, self._hand_status_callback)
        rospy.Subscriber('touch_status', TouchSensor, self._touch_status_callback)
        rospy.wait_for_service('move_to_joints')
        self.hand_joint_srv = rospy.ServiceProxy('move_to_joints', control_hand)

        self.hand_joint = None
        self.hand_status = None
        self.touch_status = None

    def num_dofs(self) -> int:
        return 12

    def _hand_joint_callback(self, data):
        self.hand_joint = np.concatenate([np.array([data.leftPosition]), np.array([data.rightPosition])])

    def _hand_status_callback(self, data):
        self.hand_status = np.array([data.data])

    def _touch_status_callback(self, data):
        self.touch_status = np.concatenate([np.array([data.left_touch]), np.array([data.right_touch])])

    def connect(self) -> bool:
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(self.hand_joint, num_of_dofs=self.num_dofs())

    def _hand_ctrl(self, type: str, value: list):
        if type == "left":
            hand_id = 2
        elif type == "right":
            hand_id = 1
        else:
            raise "hand type error"
        
        try:
            request = control_handRequest()
            request.hand_id = hand_id
            request.target_positions = value
            response = self.hand_joint_srv(request)
        except rospy.ServiceException as e:
            print(f"Service call failed: {e}")

    def sync_target_joint(self, target_joint: np.ndarray):
        left_target = target_joint[0:6].tolist()
        right_target = target_joint[6:12].tolist()

        self._hand_ctrl("left", left_target)
        self._hand_ctrl("right", right_target)


    def open(self):
        self.sync_target_joint(np.asarray([1, 1, 1, 1, 1, 0]))
