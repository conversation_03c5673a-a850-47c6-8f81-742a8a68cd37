from typing import Optional
import numpy as np
from xrocs.common.data_type import Joints
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.hand.hand_base import HandDriver


class HockHand(HandDriver):
    def __init__(self, robot_ip: str):
        self._robot_ip = robot_ip

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        logger.success("Connect to Hock Hand")
        return True

    def open(self) -> bool:
        logger.success("Open Hock Hand")
        return True

    def close(self) -> bool:
        logger.success("Close Hock Hand")
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.zeros(1), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        ...
        # logger.success(f"Set Hock Hand to {target_joint}")
        return True

    def sync_target_joint(self, target_joint: np.ndarray):
        ...
        # logger.success(f"Sync Hock Hand to {target_joint}")