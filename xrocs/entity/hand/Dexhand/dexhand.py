import time
from typing import Dict, Optional

import numpy as np
from xrocs.common.data_type import Pose, JointsArray, Joints, PoseArray, Coordinate, Rotation
from xrocs.entity.hand.hand_base import HandInterface
from pyzlg_dexhand.dexhand_interface import (
    LeftDexHand,
    RightDexHand,
    ControlMode,
    ZCANWrapper,
    HandFeedback,
)


class JointConfig:
    """Configuration for a joint including limits"""
    def __init__(self, min_angle=0.0, max_angle=90.0):
        self.min_angle = min_angle
        self.max_angle = max_angle


class X_Dexhand(HandInterface):
    def __init__(self, hand_type='right',zcan=None):
        """
        Initialize the hand object. The hand can be either left or right.

        Parameters:
        - hand_type: String specifying the hand type. Can be 'left' or 'right'. Default is 'left'.
        """
        self._hand_type = hand_type
        self._zcan = zcan
        self.JOINT_CONFIGS = {
            "th_rot": JointConfig(max_angle=150.0),  # Thumb rotation has extended range
            "th_mcp": JointConfig(),  # Default 0-90
            "th_dip": JointConfig(),
            "ff_spr": JointConfig(max_angle=30.0),  # Finger spread is limited
            "ff_mcp": JointConfig(),
            "ff_dip": JointConfig(),
            "mf_mcp": JointConfig(),
            "mf_dip": JointConfig(),
            "rf_mcp": JointConfig(),
            "rf_dip": JointConfig(),
            "lf_mcp": JointConfig(),
            "lf_dip": JointConfig(),
        }
        self._is_normalized = False
        
    def num_dofs(self) -> int:
        return 12
    
    def connect(self) -> bool:
        if self._hand_type == 'left':
            self.hand = LeftDexHand(self._zcan)
        elif self._hand_type == 'right':
            self.hand = RightDexHand(self._zcan)  # Assuming you have a RightDexHand class for the right hand
        else:
            raise ValueError("hand_type must be either 'left' or 'right'.")
        self.hand.init()

    def open(self) -> bool:
        self.goto_home()
        return True
    
    def get_current_joint(self) -> Joints:
        result = self.get_joints_position()
        return result

    def reach_target_joint(self, target_joint) -> bool:
        self.move_to_target(target_joint)
        return True

    def sync_target_joint(self, target_joint) -> bool:
        self.move_to_target(target_joint)
        return True

    def move_to_target(self, target, normalized=False):
        """
        Move the hand to the specified target positions.

        Parameters:
        - target: A list of values corresponding to the joint positions. 
                If `normalized` is True, values should be in the range [0, 1].
                If `normalized` is False, values should be the actual joint angles.
        - normalized: Boolean indicating whether the input values are normalized (default: True).
        """
        normalized = self._is_normalized
        # Ensure the target length matches the number of joints
        if len(target) != len(self.JOINT_CONFIGS):
            raise ValueError(f"Expected {len(self.JOINT_CONFIGS)} target values, but got {len(target)}.")
        # Map input values to joint angles
        joint_angle_dict = {}
        for joint_name, input_value in zip(self.JOINT_CONFIGS.keys(), target):
            joint_config = self.JOINT_CONFIGS[joint_name]
            if normalized:
                # Denormalize: Map [0, 1] to [min_angle, max_angle]
                angle = joint_config.min_angle + input_value * (joint_config.max_angle - joint_config.min_angle)
            else:
                # Use the input value directly as the angle
                angle = input_value
            if angle <joint_config.min_angle:
                angle=joint_config.min_angle
            if angle >joint_config.max_angle:
                angle=joint_config.max_angle
            # Ensure the angle is within the valid range
            if not (joint_config.min_angle <= angle <= joint_config.max_angle):
                raise ValueError(f"Joint {joint_name} angle {angle} is out of bounds [{joint_config.min_angle}, {joint_config.max_angle}].")
            
            joint_angle_dict[joint_name] = angle

        # Send joint angles to the hardware
        self.hand.move_joints(
            **joint_angle_dict,
            control_mode=ControlMode.CASCADED_PID
            # control_mode=ControlMode.PROTECT_HALL_POSITION
        )
        self.clear_err()
        return
    
    def get_feeling(self) -> HandFeedback:
        return self.hand.get_feedback()

    def get_joints_position(self, normalized=False):
        """
        Get all joint angles from the hand as a NumPy array.

        Parameters:
        - hand: The hand object that provides feedback.
        - normalized: Boolean indicating whether to return normalized angles (default: True).

        Returns:
        - A NumPy ndarray where each element is a joint angle.
        If `normalized=True`, angles are in the range [0, 1].
        If `normalized=False`, angles are the actual joint angles.
        The order follows the joint names in `JOINT_CONFIGS`.
        """
        normalized = self._is_normalized
        feedback = self.hand.get_feedback()  # Get feedback from the hand
        joint_angles = []

        for joint_name, config in self.JOINT_CONFIGS.items():
            if joint_name not in feedback.joints:
                raise KeyError(f"Joint {joint_name} not found in hand feedback.")
            
            # Get the current joint angle
            angle = feedback.joints[joint_name].angle

            if normalized:
                # Normalize the angle to the range [0, 1]
                normalized_angle = (angle - config.min_angle) / (config.max_angle - config.min_angle)
                joint_angles.append(max(0.0, min(1.0, normalized_angle)))  # Clamp to [0, 1]
            else:
                # Use the raw angle
                joint_angles.append(angle)

        return Joints(np.array(joint_angles), num_of_dofs=len(joint_angles))
    
    def goto_home(self):
        self.hand.reset_joints() 

    def clear_err(self):
        self.hand.clear_errors()


