import time
from typing import Optional
import numpy as np
from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver
from xrocs.entity.hand.Robotiq.rg_driver import RobotiqGripperDriver


class RobotiqGripper(HandDriver):
    def __init__(self, robot_ip: str):
        self.gripper = RobotiqGripperDriver()
        self._robot_ip = robot_ip

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        self.gripper.connect(hostname=self._robot_ip, port=63352)
        return True
    
    def open(self) -> bool:
        self.sync_target_joint(0)
        return True

    def close(self) -> bool:
        self.sync_target_joint(1)
        return True

    def get_current_joint(self) -> Optional[Joints]:
        time.sleep(0.01)
        gripper_pos = self.gripper.get_current_position()
        assert 0 <= gripper_pos <= 255, "Gripper position must be between 0 and 255"
        position =  gripper_pos / 255
        return Joints(np.array([position]), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: np.ndarray):
        target_joint = float(target_joint)
        assert 0.0 <= target_joint <= 1.0, "Gripper control parameter must be between 0 and 1"
        gripper_pos = target_joint * 255
        speed = 255
        force = 10
        self.gripper.move(int(gripper_pos), speed, force)