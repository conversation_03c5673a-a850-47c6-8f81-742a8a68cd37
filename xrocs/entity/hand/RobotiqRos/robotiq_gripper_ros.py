import os
from typing import Optional
import numpy as np
import rospy
from control_msgs.msg import <PERSON>ripperCommandAction, GripperCommandGoal
import actionlib
from xrocs.common.data_type import Joints
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.hand.hand_base import HandDriver


class RobotiqGripperRos(HandDriver):
    def __init__(self, hand_ip: str, local_ip: str, hand_type: str):
        topic_name = f'/robotiq_gripper_controller_{hand_type}/gripper_cmd'
        self._gripper = actionlib.SimpleActionClient(topic_name, GripperCommandAction)

        self.hand_joint = [0]

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        self._gripper.wait_for_server()
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(np.array([self.hand_joint]), num_of_dofs=self.num_dofs())

    def _send_gripper_command(self, position):
        max_effort = 1.0
        try:
            goal = GripperCommandGoal()
            goal.command.position = 1.0 - max(0.0, min(1.0, position))  # Clamp position to [0.0, 1.0]
            goal.command.max_effort = max_effort
            self._gripper.send_goal(goal)
        except Exception as e:
            rospy.logerr(f"Failed to send gripper command: {e}")

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: np.ndarray):
        position = float(target_joint[0])
        self._send_gripper_command(position)
        self.hand_joint = position

    def open(self):
        self.sync_target_joint(np.asarray([1]))
