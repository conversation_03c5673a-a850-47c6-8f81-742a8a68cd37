from typing import Optional
import time

import numpy as np

from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver
from xrocs.utils.logger.logger_loader import logger


class RobotiqGripperFrankaSim(HandDriver):
    def __init__(self, robot_driver):
        self._gripper = robot_driver

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        return True
    
    def open(self) -> bool:
        self.sync_target_joint([0])
        return True

    def close(self) -> bool:
        self.sync_target_joint([1])
        return True

    def get_current_joint(self):
        start_time = time.time()
        timeout = 5
        while time.time() - start_time < timeout:
            try:
                return self._get_current_joint()
            except TypeError as e:
                logger.error("Encountered error:", e, "— retrying after delay.")
                time.sleep(0.001)  

        raise TimeoutError("Failed to read get_current_joint within the allowed time frame.")

    def _get_current_joint(self) -> Optional[Joints]:
        gripper_pos = self._gripper.get_driver().get_all_joints_state()['gripper_joint']
        return Joints(np.array([gripper_pos]), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: np.ndarray):
        if len(target_joint) >= 1:
            target_joint = target_joint[0]
        assert 0 <= target_joint <= 1, "Gripper control parameter must be between 0 and 1"
        self._gripper.get_driver().set_gripper_joint(target_joint)
        return True
