from typing import Optional
import numpy as np
import zerorpc

from xrocs.common.data_type import Joints
from xrocs.entity.hand.hand_base import HandDriver


class RobotiqGripperClient(HandDriver):
    def __init__(self, hand_ip: str, hand_port: str):
        self.driver = zerorpc.Client(timeout=5)
        self._last_val = 0
        self._hand_ip = hand_ip
        self._hand_port = hand_port

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        self.driver.connect(f"tcp://{self._hand_ip}:{self._hand_port}")
        return True

    def open(self) -> bool:
        self.sync_target_joint(0)
        return True

    def close(self) -> bool:
        self.sync_target_joint(1)
        return True

    def get_current_joint(self) -> Optional[Joints]:
        position = self.driver.get_gripper()
        return Joints(np.array([position]), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: np.ndarray):
        target_joint = float(target_joint)
        assert 0.0 <= target_joint <= 1.0, "Gripper control parameter must be between 0 and 1"
        target_joint = round(target_joint, 1)
        self.driver.set_gripper(target_joint, 255, 255)
