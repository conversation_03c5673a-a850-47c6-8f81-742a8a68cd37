import argparse

import minimalmodbus
import zerorpc

from xrocs.entity.hand.RobotiqUSB.robotiq_driver import RobotiqGripper


class RobotiqGripperUSBServer(zerorpc.Server):
    def __init__(self, robot_port: str):
        super().__init__()
        self.gripper = RobotiqGripper(robot_port)
        self._last_val = 0
        self.gripper.activate()

    def get_gripper(self):
        try:
            self._last_val = gripper_pos = self.gripper.getPosition()
        except minimalmodbus.InvalidResponseError as error:
            print(f'minimalmodbus.InvalidResponseError retry {error}')
            gripper_pos = self._last_val

        assert 0 <= gripper_pos <= 255, "Gripper position must be between 0 and 255"
        position =  gripper_pos / 255
        return position

    def set_gripper(self, target_joint: float, speed: int = 255, force: int = 255):
        target_joint = float(target_joint)
        assert 0.0 <= target_joint <= 1.0, "Gripper control parameter must be between 0 and 1"
        gripper_pos = target_joint * 255
        try:
            self.gripper.goTo(int(gripper_pos), speed, force)
        except minimalmodbus.InvalidResponseError as error:
            print(f'minimalmodbus.InvalidResponseError retry {error}')



if __name__ == "__main__":
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument(
        "--hand_ip",
        type=str,
        default="/dev/ttyUSB0",
        help="The ip of the gripper, e.g., /dev/ttyUSB0",
    )
    arg_parser.add_argument(
        "--hand_port",
        type=str,
        default="4242",
        help="The port of the gripper, e.g., 4242",
    )
    args = arg_parser.parse_args()
    server = zerorpc.Server(RobotiqGripperUSBServer(args.hand_ip))
    server.bind(f"tcp://127.0.0.1:{args.hand_port}")  # Bind to a specific port
    print(f"ZeroRPC robotiq server running on port {args.hand_port}...")
    server.run()
