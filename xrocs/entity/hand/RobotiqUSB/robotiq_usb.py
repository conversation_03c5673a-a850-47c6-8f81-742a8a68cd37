from typing import Optional
import numpy as np
from xrocs.common.data_type import Joints
from xrocs.entity.hand.RobotiqUSB.robotiq_driver import RobotiqGripper
from xrocs.entity.hand.hand_base import HandDriver
import minimalmodbus

class RobotiqGripperUSB(HandDriver):
    def __init__(self, hand_port: str):
        self.gripper = None
        self.robot_port = hand_port
        self._last_val = 0

    def num_dofs(self) -> int:
        return 1

    def connect(self) -> bool:
        self.gripper = RobotiqGripper(self.robot_port)
        return True
    
    def open(self) -> bool:
        self.sync_target_joint(0)
        return True

    def close(self) -> bool:
        self.sync_target_joint(1)
        return True

    def get_current_joint(self) -> Optional[Joints]:
        try:
            self._last_val = gripper_pos = self.gripper.getPosition()
        except minimalmodbus.InvalidResponseError as error:
            print(f'minimalmodbus.InvalidResponseError retry')
            gripper_pos = self._last_val

        assert 0 <= gripper_pos <= 255, "Gripper position must be between 0 and 255"
        position =  gripper_pos / 255
        return Joints(np.array([position]), num_of_dofs=self.num_dofs())

    def set_target_joint(self, target_joint: Joints) -> bool:
        self.sync_target_joint(target_joint.get_radian_ndarray())

    def sync_target_joint(self, target_joint: np.ndarray):
        target_joint = float(target_joint)
        target_joint = round(target_joint, 1)
        assert 0.0 <= target_joint <= 1.0, "Gripper control parameter must be between 0 and 1"
        gripper_pos = target_joint * 255
        speed = 255
        force = 255
        try:
            self.gripper.goTo(int(gripper_pos), speed, force)
        except minimalmodbus.InvalidResponseError as error:
            print(f'minimalmodbus.InvalidResponseError retry')