import threading
from typing import Dict, <PERSON><PERSON>, Any

import numpy as np

from xrocs.entity.camera.isaac_camera import <PERSON>Came<PERSON>
from xrocs.entity.robot.FrankaSim.franka_sim import FrankaSimRobot
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.hand.RobotiqFrankaSim.robotiq_gripper_sim import RobotiqGripperFrankaSim


class FrankaSimStation(RobotEnvBase):

    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls,
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(FrankaSimStation, cls).__new__(cls)
        return FrankaSimStation._instance

    def __init__(self,
                 robot_dict: dict[str, str],
                 camera_dict: dict[str, str],
                 hand_dict: dict[str, str]):
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = <PERSON>aSimRobot(robot_ip=ip, arm_type=name)
        self._camera_dict = {}
        for name, port in camera_dict.items():
            self._camera_dict[name] = IsaacCamera(self._robot_dict[port], name)
        self._hand_dict = {}
        for name, port in hand_dict.items():
            self._hand_dict[name] = RobotiqGripperFrankaSim(self._robot_dict[name])
        self.control_rate_hz = 30
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def get_robot_state(self) -> Dict[str, Any]:
        observations = {}
        for name, arm in self._robot_dict.items():
            val = arm.get_all_joints_state()
            observations = {
                "arm_joints":
                    {name: np.asarray(val['arm_joint'])},
                "arm_pose":
                    {name: np.asarray(val['ee_pose'])},
                "hand_joints":
                    {name: np.asarray(val['gripper_joint'])}
            }
        return observations

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
                "arm_joints": {
                    "robot": action[0:7],
                },
                "hand_joints": {
                    "robot": np.asarray([action[7]]),
                }
            }

    def close(self):
        for name, arm in self._robot_dict.items():
            arm.close()
    