import threading
import os
from typing import Any, Dict, <PERSON><PERSON>

import numpy as np
import rospy

from xrocs.entity.camera.tienkung_camera import TienKungCamera
from xrocs.entity.hand.RobotiqUSB.robotiq_usb import RobotiqGripperUSB
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.robot.XRobot.tienkung2 import Tienkung2Robot
from xrocs.utils.logger.logger_loader import logger

REMOTE_IP = "************"
os.environ["ROS_MASTER_URI"] = f"http://{REMOTE_IP}:11311"
os.environ["ROS_IP"] = "*************"


class X2Gripper(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(<PERSON>2<PERSON><PERSON><PERSON>, cls).__new__(cls)
        return X2Gripper._instance

    def __init__(self, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        
        logger.success("Tienkung2Robot Node")
        rospy.init_node('Tienkung2RobotNode')
        logger.success("Tienkung2Robot Node Initialized")

        local_ip = '127.0.0.1'
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = Tienkung2Robot(robot_ip=ip, local_ip=local_ip)
        self._camera_dict = {}
        for name, cam_name in camera_dict.items():
            self._camera_dict[name] = TienKungCamera(name)
        self._hand_dict = {}
        for name, robot_port in hand_dict.items():
            self._hand_dict[name] = RobotiqGripperUSB(hand_port=robot_port)
        self.control_rate_hz = 400
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def get_robot_state(self) -> Dict[str, Any]:
        observations = {
            "arm_joints":
                {name: arm.get_current_joint().get_radian_ndarray() for name, arm in self._robot_dict.items()},
            "arm_pose":
                {},
            "hand_joints":
                {name: hand.get_current_joint().get_radian_ndarray() for name, hand in self._hand_dict.items()}
        }
        return observations

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "robot": np.concatenate([action[0:7], action[8:15]])
            },
            "hand_joints": {
                "left": np.asarray([action[7]]),
                "right": np.asarray([action[15]])
            }
        }
