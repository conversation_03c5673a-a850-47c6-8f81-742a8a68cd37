import threading
import os
from typing import <PERSON><PERSON>

import numpy as np
import rospy

from xrocs.common.logger_loader import logger
from xrocs.entity.camera.tienkung_camera import TienKungCamera
from xrocs.entity.hand.InspireHand.inspire_hand import InspiredHand
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.robot.XRobot.tienkung import TienkungRobot


os.environ["ROS_MASTER_URI"] = "http://************:11311"
os.environ["ROS_IP"] = "************"


class XProDHand(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls,
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str],
                is_intrp: bool):
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(XProDHand, cls).__new__(cls)
        return XProDHand._instance

    def __init__(self,
                 robot_dict: dict[str, str],
                 camera_dict: dict[str, str],
                 hand_dict: dict[str, str],
                 is_intrp: bool = False
                ):
        
        logger.success("TienkungRobot Node")
        rospy.init_node('TienkungRobotNode')
        logger.success("TienkungRobot Node Initialized")

        local_ip = '************'
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = TienkungRobot(robot_ip=ip, local_ip=local_ip, is_intrp=is_intrp)
        self._camera_dict = {}
        for name, cam_name in camera_dict.items():
            self._camera_dict[name] = TienKungCamera(cam_name)
        self._hand_dict = {}
        for name, robot_ip in hand_dict.items():
            self._hand_dict[name] = InspiredHand(hand_ip=robot_ip, local_ip=local_ip, hand_type=name)
        self.control_rate_hz = 100
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "robot": np.concatenate([action[0:7], action[8:15]])
            },
            "hand_joints": {
                "left": np.asarray([action[7]]),
                "right": np.asarray([action[15]])
            }
        }
