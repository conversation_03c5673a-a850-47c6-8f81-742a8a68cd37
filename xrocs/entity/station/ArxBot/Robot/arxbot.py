from typing import Optional, List
import numpy as np
import rospy 

from xrocs.common.data_type import Joints
from xrocs.common.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver
from xrocs.entity.station.ArxBot.msg._JointInformation import JointInformation
from xrocs.entity.station.ArxBot.msg._JointControl import JointControl
from xrocs.entity.station.ArxBot.msg._PosCmd import PosCmd
from xrocs.common.data_type import Pose, Rotation, Coordinate


class ArxBot(RobotArmDriver):
    def __init__(self, robot_type: str) -> None:
        self.robot_type = robot_type
        if robot_type == 'left':
            self.puppet_arm_publisher = rospy.Publisher('/joint_control', JointControl, queue_size=10)
            rospy.Subscriber('/joint_information', JointInformation, self._puppet_arm_joint_callback, queue_size=10)
            rospy.Subscriber('/follow1_pos_back', PosCmd, self._puppet_ee_pose_callback, queue_size=10)
        elif robot_type == 'right':
            self.puppet_arm_publisher = rospy.Publisher('/joint_control2', JointControl, queue_size=10)
            rospy.Subscriber('/joint_information2', JointInformation, self._puppet_arm_joint_callback, queue_size=10)
            rospy.Subscriber('/follow2_pos_back', PosCmd, self._puppet_ee_pose_callback, queue_size=10)
        self.rate = rospy.Rate(100)
        self.joint_state:List = None
        self.end_coor = Coordinate([0,0,0])
        self.end_rot = Rotation([0,0,0,0], rotation_form_type=Rotation.FormType.XYZW)
        
    def num_dofs(self) -> int:
        return 7

    def connect(self) -> bool:
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(self.joint_state, len(self.joint_state))

    def reach_target_joint(self, target_joint: Joints) -> bool:
        fine_step = 100
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 target_joint.get_radian_ndarray(), fine_step)
        for stp in step_array:
            self.sync_target_joint(stp)
            self.rate.sleep()
        return True
    
    def _construct_target_msg(self, target_joint: List[float]) -> JointControl:
        joint_state_msg = JointControl()
        joint_state_msg.joint_pos = target_joint
        return joint_state_msg

    def sync_target_joint(self, target_joint: np.ndarray) -> None:
        self.puppet_arm_publisher.publish(self._construct_target_msg(target_joint.tolist()))
        return

    def _puppet_arm_joint_callback(self, data):
        self.joint_state = data.joint_pos

    def _puppet_ee_pose_callback(self, data):
        self.end_coor, self.end_rot = self._decode_pose_msg(data)
    
    def _decode_pose_msg(self, pose):
        position = [
            pose.x,
            pose.y,
            pose.z
        ]
        orientation = [
            pose.quater_x,
            pose.quater_y,
            pose.quater_z,
            pose.quater_w
        ]
        return Coordinate(list(position)), Rotation(list(orientation), rotation_form_type=Rotation.FormType.XYZW)

    def get_tool_cartesian_pose(self):
        return [self.end_coor.get_xyz_m_ndarray().tolist(), self.end_rot.get_rpy_radian_ndarray().tolist()]

    # To do
    def reach_tool_cartesian_pose(self):
        pass

    def sync_tool_cartesian_pose(self):
        pass