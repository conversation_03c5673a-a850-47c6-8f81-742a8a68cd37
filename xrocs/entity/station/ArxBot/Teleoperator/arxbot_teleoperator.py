import numpy as np
import rospy
from xrocs.common.logger_loader import logger
from xrocs.entity.station.ArxBot.msg._PosCmd import PosCmd

class ArxbotTeleoperator:
    def __init__(self):
        rospy.Subscriber('/ARX_VR_L', PosCmd, self.tele_callback_left, queue_size=10)
        rospy.Subscriber('/ARX_VR_R', PosCmd, self.tele_callback_right, queue_size=10)
        self.tele_joints_left = None
        self.tele_joints_right = None
        logger.success("Agilex teleoperator node")
    
    def tele_callback_left(self, data):
        xyz = [data.x, data.y, data.z]
        rpy = [data.roll, data.pitch, data.yaw]
        gripper = [data.gripper]
        head = [data.height, data.head_pit, data.head_yaw]
        self.tele_joints_left = np.array(xyz+rpy+gripper+head)

    def tele_callback_right(self, data):
        xyz = [data.x, data.y, data.z]
        rpy = [data.roll, data.pitch, data.yaw]
        gripper = [data.gripper]
        head = [data.height, data.head_pit, data.head_yaw]
        self.tele_joints_right = np.array(xyz+rpy+gripper+head)

    def act(self):
        return np.concatenate((self.tele_joints_left, self.tele_joints_right))
    

if __name__ == "_main__":
    aa = ArxbotTeleoperator()