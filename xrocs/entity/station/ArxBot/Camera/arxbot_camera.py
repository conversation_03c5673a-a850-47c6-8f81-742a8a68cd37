from typing import <PERSON>ple
import numpy as np
import cv2
import rospy
import time
from sensor_msgs.msg import Image 
from message_filters import Subscriber, ApproximateTimeSynchronizer
import cv_bridge
from xrocs.common.logger_loader import logger
from xrocs.entity.camera.camera_base import CameraDriver

class ArxbotCamera(CameraDriver):
    def __init__(self, camera_type:str):
        if camera_type == 'left':
            rgb_topic = '/camera_l/color/image_raw'
            depth_topic = '/camera_l/depth/image_rect_raw'
        elif camera_type == 'head':
            rgb_topic = '/camera_h/color/image_raw'
            depth_topic = '/camera_h/depth/image_rect_raw'
        elif camera_type == 'right':
            rgb_topic = '/camera_r/color/image_raw'
            depth_topic = '/camera_r/depth/image_rect_raw'

        img_sub = Subscriber(rgb_topic, Image)
        depth_sub = Subscriber(depth_topic, Image)
        ats = ApproximateTimeSynchronizer(
            [img_sub, depth_sub],
            queue_size=10,
            slop=0.1,
            allow_headerless=False
        )
        ats.registerCallback(self.image_callback)
        self.bridge = cv_bridge.CvBridge()
        self.image = None 
        self.depth = None
        time.sleep(1)
        logger.success("Agilex camera node")
        self.rate = rospy.Rate(100)

    def image_callback(self, image_msg, depth_msg):
        try:
            # Convert ROS Image messages to OpenCV images
            self.image = self.bridge.imgmsg_to_cv2(image_msg, desired_encoding='passthrough')
            self.depth = self.bridge.imgmsg_to_cv2(depth_msg, desired_encoding='passthrough')
        except Exception as e:
            rospy.logerr("Error converting images: %s", e)

    def read(self) -> Tuple[np.ndarray, np.ndarray]:
        _, rgb = cv2.imencode(".jpg", self.image)
        depth_uint16 = self.depth.astype(np.uint16)
        _, depth = cv2.imencode(".png", depth_uint16)
        return rgb, depth

if __name__ == "__init__":
    aa = ArxbotCamera()
    aa.read()