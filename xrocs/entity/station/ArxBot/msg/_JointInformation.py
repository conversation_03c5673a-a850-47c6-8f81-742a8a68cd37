# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from arm_control/JointInformation.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class JointInformation(genpy.Message):
  _md5sum = "86ad2a9a176a812901cac6f69b936f2e"
  _type = "arm_control/JointInformation"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """float32[7] joint_pos
float32[7] joint_vel
float32[7] joint_cur
int32 mode"""
  __slots__ = ['joint_pos','joint_vel','joint_cur','mode']
  _slot_types = ['float32[7]','float32[7]','float32[7]','int32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       joint_pos,joint_vel,joint_cur,mode

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(JointInformation, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.joint_pos is None:
        self.joint_pos = [0.] * 7
      if self.joint_vel is None:
        self.joint_vel = [0.] * 7
      if self.joint_cur is None:
        self.joint_cur = [0.] * 7
      if self.mode is None:
        self.mode = 0
    else:
      self.joint_pos = [0.] * 7
      self.joint_vel = [0.] * 7
      self.joint_cur = [0.] * 7
      self.mode = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      buff.write(_get_struct_7f().pack(*self.joint_pos))
      buff.write(_get_struct_7f().pack(*self.joint_vel))
      buff.write(_get_struct_7f().pack(*self.joint_cur))
      _x = self.mode
      buff.write(_get_struct_i().pack(_x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 28
      self.joint_pos = _get_struct_7f().unpack(str[start:end])
      start = end
      end += 28
      self.joint_vel = _get_struct_7f().unpack(str[start:end])
      start = end
      end += 28
      self.joint_cur = _get_struct_7f().unpack(str[start:end])
      start = end
      end += 4
      (self.mode,) = _get_struct_i().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      buff.write(self.joint_pos.tostring())
      buff.write(self.joint_vel.tostring())
      buff.write(self.joint_cur.tostring())
      _x = self.mode
      buff.write(_get_struct_i().pack(_x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 28
      self.joint_pos = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=7)
      start = end
      end += 28
      self.joint_vel = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=7)
      start = end
      end += 28
      self.joint_cur = numpy.frombuffer(str[start:end], dtype=numpy.float32, count=7)
      start = end
      end += 4
      (self.mode,) = _get_struct_i().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_7f = None
def _get_struct_7f():
    global _struct_7f
    if _struct_7f is None:
        _struct_7f = struct.Struct("<7f")
    return _struct_7f
_struct_i = None
def _get_struct_i():
    global _struct_i
    if _struct_i is None:
        _struct_i = struct.Struct("<i")
    return _struct_i
