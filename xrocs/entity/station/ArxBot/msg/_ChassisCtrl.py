# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from arm_control/ChassisCtrl.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class ChassisCtrl(genpy.Message):
  _md5sum = "488e9407ad3cab6f3db0659c324cb1ea"
  _type = "arm_control/ChassisCtrl"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """float64 vx
float64 vy
float64 vz
int32 mode1
int32 mode2"""
  __slots__ = ['vx','vy','vz','mode1','mode2']
  _slot_types = ['float64','float64','float64','int32','int32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       vx,vy,vz,mode1,mode2

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(ChassisCtrl, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.vx is None:
        self.vx = 0.
      if self.vy is None:
        self.vy = 0.
      if self.vz is None:
        self.vz = 0.
      if self.mode1 is None:
        self.mode1 = 0
      if self.mode2 is None:
        self.mode2 = 0
    else:
      self.vx = 0.
      self.vy = 0.
      self.vz = 0.
      self.mode1 = 0
      self.mode2 = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3d2i().pack(_x.vx, _x.vy, _x.vz, _x.mode1, _x.mode2))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 32
      (_x.vx, _x.vy, _x.vz, _x.mode1, _x.mode2,) = _get_struct_3d2i().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3d2i().pack(_x.vx, _x.vy, _x.vz, _x.mode1, _x.mode2))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 32
      (_x.vx, _x.vy, _x.vz, _x.mode1, _x.mode2,) = _get_struct_3d2i().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3d2i = None
def _get_struct_3d2i():
    global _struct_3d2i
    if _struct_3d2i is None:
        _struct_3d2i = struct.Struct("<3d2i")
    return _struct_3d2i
