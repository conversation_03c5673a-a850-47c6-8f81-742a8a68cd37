import threading
import rospy
from xrocs.entity.station.ArxBot.Robot.arxbot import ArxBot
from xrocs.entity.station.ArxBot.Camera.arxbot_camera import ArxbotCamera
from xrocs.entity.station.ArxBot.Chassis.arxbot_chassis import <PERSON>rx<PERSON><PERSON>has<PERSON>
from xrocs.entity.station.robot_env import RobotEnvBase
from typing import Dict as dict
from typing import Any, <PERSON>ple
import numpy as np
import os

os.environ["ROS_MASTER_URI"] = "http://127.0.0.1:11311"
os.environ["ROS_IP"] = "127.0.0.1"

class ArxbotStation(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(
            cls, 
            robot_dict: dict[str, str],
            camera_dict: dict[str, str]
        ):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(ArxbotStation, cls).__new__(cls)
        return ArxbotStation._instance

    def __init__(
            self, 
            robot_dict: dict[str, str],
            camera_dict: dict[str, str]
        ):
        rospy.init_node('ArxBot', anonymous=True)
        self.control_rate_hz = 100
        self.rate = rospy.Rate(self.control_rate_hz)
        self.is_infer = False
        
        # "left","right"
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = ArxBot(robot_type=name)

        # "front", "left", "right", ...
        self._camera_dict = {}
        for name, port in camera_dict.items():
            self._camera_dict[name] = ArxbotCamera(camera_type=name)

        self._hand_dict = {}
        self._chassis_dict = {}
        self._chassis_dict['chassis'] = ArxbotChassis()

        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def get_chassis_handle(self):
        return self._chassis_dict
    
    def assemble_station(self, action_dict: dict) -> Tuple[dict, dict]:
        robot_targets = {'arm_joints': {}}
        for name in self._robot_dict.keys():
            robot_targets['arm_joints'][name] = action_dict['arm_joints'][name]
        hand_targets = {}
        return robot_targets, hand_targets

    def step(self, robot_joints: dict[str, dict], hand_target: dict[str, dict]):
        if self.is_infer:
            for name, joints in robot_joints['arm_joints'].items():
                self._robot_dict[name].sync_target_joint(joints)
            self._chassis_dict['chassis'].sync_target_twist(robot_joints['chassis_twist'])
        else:
            pass
        self.rate.sleep()
        return self.get_obs()
    
    def get_robot_state(self) -> dict[str, Any]:
        observations = {
            "arm_joints":
                {name: arm.get_current_joint().get_radian_ndarray() for name, arm in self._robot_dict.items()},
            "arm_pose":
                {name: arm.get_tool_cartesian_pose() for name, arm in self._robot_dict.items()},
            "chassis_state": 
                {name: value for name, value in self._chassis_dict['chassis'].get_chassis_state().items()},
            "hand_joints": {}
        }
        return observations
    
    def decompose_action(self, action) -> dict:
        return {
            "arm_joints": {
                "left": action[0:7],
                "right": action[7:14]
            },
        }