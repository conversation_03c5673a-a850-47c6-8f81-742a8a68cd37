import time
import rospy
import tqdm
import numpy as np
from typing import Optional, Dict, Any
from xrocs.common.logger_loader import logger
from xrocs.entity.station.ArxBot.msg._PosCmd import PosCmd


class ArxbotChassis():
    def __init__(self) -> None:
        rospy.Subscriber('/ARX_VR_L', PosCmd, self._chassis_callback, queue_size=10)
        self._chassis_coor:list = None
        self.rate = rospy.Rate(100)
        self.seq = 0
        time.sleep(1.5)

    
    def _chassis_callback(self, data):
        position = [
            data.chx,
            data.chy,
            data.chz,
            data.height,
            data.head_pit,
            data.head_yaw
        ]
        self._chassis_coor = position
    
    def get_chassis_state(self) -> Dict[str, Any]:
        observations = {
            "chassis_coor": self._chassis_coor
        }
        return observations