import threading

import numpy as np

from xrocs.entity.camera.orbbec_camera import OrbbecClientCamera
from xrocs.entity.hand.Robotiq.robotiq_gripper import RobotiqGripper
from xrocs.entity.robot.UR.ur import URRobot
from xrocs.entity.station.robot_env import RobotEnvBase


class URStdStation(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls,
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str]):
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(URStdStation, cls).__new__(cls)
        return URStdStation._instance

    def __init__(self,
                 robot_dict: dict[str, str],
                 camera_dict: dict[str, str],
                 hand_dict: dict[str, str]):
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = URRobot(robot_ip=ip)
        self._camera_dict = {}
        for name, value in camera_dict.items():
            self._camera_dict[name] = OrbbecClientCamera(value)
        self._hand_dict = {}
        for name, robot_ip in hand_dict.items():
            self._hand_dict[name] = RobotiqGripper(robot_ip=robot_ip)
        self.control_rate_hz = 100
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        if 'single' in self._robot_dict:
            return {
                "arm_joints": {
                    "single": action[0:6],
                },
                "hand_joints": {
                    "single": np.asarray([action[6]]),
                }
           }
        else:
            return {
                "arm_joints": {
                    "left": action[0:6],
                    "right": action[7:13],
                },
                "hand_joints": {
                    "left": np.asarray([action[6]]),
                    "right": np.asarray([action[13]]),
                }
            }
