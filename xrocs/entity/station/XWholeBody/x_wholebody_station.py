import threading
import os
import time
import numpy as np
import rospy
from typing import <PERSON>ple, Any
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.camera.tienkung_camera import <PERSON>ienKungCamera
from xrocs.entity.hand.RobotiqRos.robotiq_gripper_ros import RobotiqGripperRos
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.robot.XRobot.tienkung import TienkungRobot
from xrocs.entity.station.XWholeBody.Waist.Waist import Waist
from xrocs.entity.station.XWholeBody.Chassis.ChassisMove import ChassisMove
from xrocs.entity.station.XWholeBody.Head.Head import HeadCtrl

os.environ["ROS_MASTER_URI"] = "http://************:11311"
os.environ["ROS_IP"] = "************"

class XWholeBody(RobotEnvBase):
    """
    头、双臂、双手、1Dof腰、升降、底盘运动
    """
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, 
                robot_dict: dict[str, str],
                camera_dict: dict[str, str], 
                hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(XWholeBody, cls).__new__(cls)
        return XWholeBody._instance

    def __init__(self, 
                 robot_dict: dict[str, str],
                 camera_dict: dict[str, str], 
                 hand_dict: dict[str, str]):
        
        logger.success("TienkungRobot Node")
        rospy.init_node('TienkungRobotWholeBody')
        logger.success("TienkungRobot Node Initialized")

        # self.m_pilot = RoboPilot('/dev/ttyACM1', '/dev/ttyACM2')
        # self.m_pilot.run()
        # self.head_target, self.waist_target, self.chassis_target = self.m_pilot.act()
        self.m_chassis = ChassisMove()
        self.m_waist = Waist()
        self.m_head = HeadCtrl()

        local_ip = '************'
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = TienkungRobot(robot_ip=ip, local_ip=local_ip)
        self._camera_dict = {}
        for name, cam_name in camera_dict.items():
            self._camera_dict[name] = TienKungCamera(cam_name)
        self._hand_dict = {}
        for name, robot_ip in hand_dict.items():
            self._hand_dict[name] = RobotiqGripperRos(hand_ip=robot_ip, local_ip=local_ip, hand_type=name)
        self.control_rate_hz = 100
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")
    
    def assemble_station(self, action_dict: dict) -> Tuple[dict, dict]:
        robot_targets = {'arm_joints': {}}
        hand_targets = {'hand_joints': {}}
        for name in self._robot_dict.keys():
            robot_targets['arm_joints'][name] = action_dict['arm_joints'][name]
        for name in self._hand_dict.keys():
            hand_targets['hand_joints'][name] = action_dict['hand_joints'][name]
        return robot_targets, hand_targets
    
    def get_robot_state(self) -> dict[str, Any]:
        observations = {
            "arm_joints":
                {name: arm.get_current_joint().get_radian_ndarray() for name, arm in self._robot_dict.items()},
            "hand_joints":
                {name: hand.get_current_joint().get_radian_ndarray() for name, hand in self._hand_dict.items()},
            "head_joints":
                {"head": self.m_head.get_crrt_pos()},
            "wrist_joints":
                {"wrist": self.m_waist.get_crrt_state()},
        }
        return observations

    def step(self, robot_targets: dict[str, dict], hand_target: dict[str, dict]):
        for name, joints in robot_targets['arm_joints'].items():  # 15
            self._robot_dict[name].sync_target_joint(joints)
        for name, joints in hand_target['hand_joints'].items():   # 2
            self._hand_dict[name].sync_target_joint(joints)

        # self.head_target, self.waist_target, self.chassis_target = self.m_pilot.act()
        # self.m_head.sync_target_pos(self.head_target)
        # self.m_waist.sync_target_state(self.waist_target)
        # self.m_chassis.sync_pilot(self.chassis_target[0], self.chassis_target[1])

        time.sleep(1/self.control_rate_hz)
        return self.get_obs()
    
    def decompose_action(self, action) -> dict:
        return {
            "arm_joints": {
                "robot": np.concatenate([action[0:7], action[8:15]])
            },
            "hand_joints": {
                "left": np.asarray([action[7]]),
                "right": np.asarray([action[15]])
            },
            "waist_joints": {
                "waist": self.waist_target[0],
                "lift": self.waist_target[1]
            },
            "head_joints": {
                "head": self.head_target
            },
            "chassis_twist": {
                "twist": self.chassis_target
            }
        }
