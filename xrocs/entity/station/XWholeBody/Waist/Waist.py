import time
import os
import numpy as np
np.set_printoptions(suppress=True)
import rospy
import tqdm
from std_msgs.msg import Header
from bodyctrl_msgs.msg import MotorStatusMsg, CmdSetMotorPosition, SetMotorPosition
from human_arm_ae_testPub.srv import Movement, MovementRequest


# waist: rad;  height: m
class Waist():
    def __init__(self):
        self.waist_publisher = rospy.Publisher('/waist/cmd_pos', CmdSetMotorPosition, queue_size = 2)
        rospy.Subscriber('/waist/status', MotorStatusMsg, self._waist_callback)
        rospy.wait_for_service('/Wheeled_Movement')
        self.wheeled_movement_service = rospy.ServiceProxy('/Wheeled_Movement', Movement)

        self.waist_pos = None
        self.height = 0
        self.rate = 100
        for _ in tqdm.tqdm(range(10), desc="Warm Up Node"):
            time.sleep(0.1)
        self._lift_ctrl(0)
    
    def _waist_callback(self, data):
        self.waist_pos = data.status[0].pos

    def get_crrt_state(self) -> np.ndarray:
        return np.array([self.waist_pos, self.height])
    
    def sync_target_state(self, target_state: np.ndarray):
        waist_target = target_state[0]
        height_target = target_state[1]
        self.waist_publisher.publish(self._construct_waist_msg(waist_target))
        self._lift_ctrl(height_target)

    def reach_target_joint(self, target_joint: np.ndarray) -> bool:
        fine_step = int(self.rate * 1.5)
        step_array = np.linspace(self.get_crrt_state(),
                                 np.array(target_joint), fine_step)
        for stp in step_array:
            self.waist_publisher.publish(self._construct_waist_msg(stp[0]))
            self._lift_ctrl(stp[1])
            time.sleep(1/self.rate)
        return True

    def _lift_ctrl(self, height: float):
        try:
            request = MovementRequest()
            request.name = 17
            request.value = height*1000
            self.height = height
            response = self.wheeled_movement_service(request)
            # print(f"Service call succeeded. Response: {response}")
        except rospy.ServiceException as e:
            print(f"Service call failed: {e}")

    def _construct_waist_msg(self, target_joints: float):
        msg = CmdSetMotorPosition()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.header.frame_id = 'waist'
        t_cmd = SetMotorPosition()
        t_cmd.name = 11
        t_cmd.pos = target_joints
        t_cmd.spd = 1
        msg.cmds = [t_cmd]
        return msg
