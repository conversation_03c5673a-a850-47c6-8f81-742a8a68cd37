import rospy
import time
from typing import Optional
import numpy as np
import rospy
import tqdm
import os
from sensor_msgs.msg import JointState
from std_msgs.msg import Header
from xrocs.common.data_type import Pose, Joints
from xrocs.utils.FlexJoints import Flex
from bodyctrl_msgs.msg import CmdSetMotorPosition, SetMotorPosition, MotorStatusMsg, MotorStatus
from xrocs.entity.robot.XRobot.tienkung import TienkungRobot

class HeadCtrl:
    def __init__(self):
        self.head_publisher = rospy.Publisher("/head/cmd_pos", CmdSetMotorPosition, queue_size=10)
        rospy.Subscriber('/head/status', MotorStatusMsg, self._head_callback)
        self.freq = 100      # Hz
        self.crrt_pos = None
        self.max_speed = 1
        time.sleep(1)

    def sync_target_pos(self, target_rpy: np.ndarray):
        target_rpy[0] = 0
        return self.reach_target_rpy(target_rpy)

    def reach_target_rpy(self, target_pos: np.ndarray) -> bool:
        msg, dur_time = self._construct_msg(target_pos)
        self.head_publisher.publish(msg)
        time.sleep(dur_time)
        return True

    def _construct_msg(self, target_rpy: np.ndarray) -> CmdSetMotorPosition:
        msg = CmdSetMotorPosition()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        cmd_1 = SetMotorPosition()
        cmd_1.name = 1
        cmd_1.spd = self.max_speed
        cmd_1.pos = target_rpy[0]
        cmd_2 = SetMotorPosition()
        cmd_2.name = 2
        cmd_2.spd = self.max_speed
        cmd_2.pos = target_rpy[1]
        cmd_3 = SetMotorPosition()
        cmd_3.name = 3
        cmd_3.spd = self.max_speed
        cmd_3.pos = target_rpy[2]
        msg.cmds = [cmd_1, cmd_2, cmd_3]
        crrt_pos = self.crrt_pos
        pos_delta = target_rpy - crrt_pos
        dur_time = max(abs(pos_delta)) / self.max_speed
        return msg, dur_time
    
    def _head_callback(self, data: MotorStatusMsg):
        self.crrt_pos = [data.status[0].pos, data.status[1].pos, data.status[2].pos]

    def get_crrt_pos(self) -> np.ndarray:
        return self.crrt_pos
