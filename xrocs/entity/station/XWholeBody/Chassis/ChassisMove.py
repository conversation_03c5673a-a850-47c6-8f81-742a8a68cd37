import rospy
from sensor_msgs.msg import Joy
from std_msgs.msg import Head<PERSON>
from tqdm import *
import time
import numpy as np
import os

class ChassisMove():
    def __init__(self):
        self.chassis_publisher = rospy.Publisher("/sbus_data", Joy, queue_size=10)
        self.dis_acc = 0.1  # m/s^2
        self.dis_vel = 0.2  # m/s
        self.ang_acc = 0.15  # rad/s^2
        self.ang_vel = 0.3  # rad/s
        self.freq = 100      # Hz
        self.coor_move = 2.14     #2.14
        self.coor_turn = 1.2     #1.20
        for i in tqdm(range(10)):
            time.sleep(0.1)
    
    def sync_pilot(self, velo_move, velo_turn):
        self.chassis_publisher.publish(self._construct_integrated_msg(velo_move, velo_turn))
        time.sleep(1/self.freq)
        return
    
    def move_straight(self, distance: float) -> bool:
        vel_array = self._gen_move_straight_array(distance)
        for tmp_vel in vel_array:
            self.chassis_publisher.publish(self._construct_move_msg(tmp_vel))
            time.sleep(1/self.freq)
        return True

    def turn_clockwise(self, angle: float) -> bool:
        vel_array = self._gen_turn_clockwise_array(angle)
        for tmp_vel in vel_array:
            self.chassis_publisher.publish(self._construct_turn_msg(tmp_vel))
            time.sleep(1/self.freq)
        return True
     
    def _gen_move_straight_array(self, distance: float) -> np.ndarray:
        if distance < 0:
            param_minus = -1
            distance = abs(distance)
        else:
            param_minus = 1
        if distance > (2 * self._cal_move_acc_process()):
            remain = distance - 2 * self._cal_move_acc_process()
            part_acc = np.linspace(0, self.dis_vel, int(self.dis_vel * self.freq/self.dis_acc))
            part_sta = np.linspace(self.dis_vel, self.dis_vel, int(remain * self.freq/ self.dis_vel))
            part_deacc = np.linspace(self.dis_vel, 0, int(self.dis_vel * self.freq/self.dis_acc))
            print(sum(np.concatenate([part_acc, part_sta, part_deacc])/100))
            return np.concatenate([part_acc, part_sta, part_deacc]) * self.coor_move * param_minus
        else:
            delta_t = np.sqrt(distance / self.dis_acc)
            part_acc = np.linspace(0, delta_t * self.dis_acc, int(self.freq * delta_t))
            part_deacc = np.linspace(delta_t * self.dis_acc, 0, int(self.freq * delta_t))
            print(sum(np.concatenate([part_acc, part_deacc]))/100)
            return np.concatenate([part_acc, part_deacc]) * self.coor_move * param_minus


    def _gen_turn_clockwise_array(self, angle: float) -> np.ndarray:
        if angle < 0:
            param_minus = -1
            angle = abs(angle)
        else:
            param_minus = 1
        if angle > 2 * self._cal_turn_acc_process():
            remain = angle - 2 * self._cal_turn_acc_process()
            part_acc = np.linspace(0, self.ang_vel, int(self.ang_vel * self.freq/self.ang_acc))
            part_sta = np.linspace(self.ang_vel, self.ang_vel, int(remain * self.freq/ self.ang_vel))
            part_deacc = np.linspace(self.ang_vel, 0, int(self.ang_vel * self.freq/self.ang_acc))
            print(sum(np.concatenate([part_acc, part_sta, part_deacc]))/100)
            print(len(np.concatenate([part_acc, part_sta, part_deacc])))
            return np.concatenate([part_acc, part_sta, part_deacc]) * self.coor_turn * param_minus
        else:
            delta_t = np.sqrt(angle / self.ang_acc)
            part_acc = np.linspace(0, delta_t * self.ang_acc, int(self.freq * delta_t))
            part_deacc = np.linspace(delta_t * self.ang_acc, 0, int(self.freq * delta_t))
            print(sum(np.concatenate([part_acc, part_deacc]))/100)
            print(len(np.concatenate([part_acc, part_deacc])))
            return np.concatenate([part_acc, part_deacc]) * self.coor_turn * param_minus
    
    def _cal_move_acc_process(self):
        return np.power(self.dis_vel, 2) / (self.dis_acc * 2)
    
    def _cal_turn_acc_process(self):
        return np.power(self.ang_vel, 2) / (self.ang_acc * 2)
    
    def _construct_integrated_msg(self, velo_move, velo_turn):
        msg = Joy()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.axes = [0] * 12
        msg.buttons = []
        msg.axes[2] = velo_move
        msg.axes[3] = velo_turn
        msg.axes[4] = -1.0
        for i in range(8, 12):
            msg.axes[i] = -1.0
        return msg

    def _construct_move_msg(self, dis: float):
        msg = Joy()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.axes = [0] * 12
        msg.buttons = []
        msg.axes[2] = dis
        msg.axes[4] = -1.0
        for i in range(8, 12):
            msg.axes[i] = -1.0
        return msg

    def _construct_turn_msg(self, angle: float):
        msg = Joy()
        msg.header = Header()
        msg.header.stamp = rospy.Time.now()
        msg.axes = [0] * 12
        msg.buttons = []
        msg.axes[3] = angle
        msg.axes[4] = -1.0
        for i in range(8, 12):
            msg.axes[i] = -1.0
        return msg
        