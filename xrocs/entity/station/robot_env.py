from abc import abstractmethod
from typing import Optional, Dict, Any, <PERSON><PERSON>
import numpy as np
from xrocs.utils.Rate import Rate
from xrocs.entity.camera.camera_base import CameraDriver
from xrocs.entity.hand.hand_base import HandDriver
from xrocs.entity.robot.robot_base import RobotArmDriver

class RobotEnvBase:
    def __init__(
            self,
            robot_dict: Dict[str, RobotArmDriver] = None,
            camera_dict: Optional[Dict[str, CameraDriver]] = None,
            hand_dict: Optional[Dict[str, HandDriver]] = None,
            control_rate_hz: float = 100.0,
            is_intrp = False
    ) -> None:
        
        # Param of if use interpolator
        self.is_intrp = is_intrp
        
        # Instantiate robots in env.
        self._robot_dict: Dict[str, RobotArmDriver] = robot_dict

        # Instantiate cameras in env.
        self._camera_dict: Dict[str, CameraDriver] = camera_dict

        # Instantiate gripper in env.
        self._hand_dict: Dict[str, HandDriver] = hand_dict

        if self._robot_dict is None:
            self._robot_dict = {}
        if self._camera_dict is None:
            self._camera_dict = {}
        if self._hand_dict is None:
            self._hand_dict = {}

        # Define control rate.
        self._rate = Rate(control_rate_hz)

    def connect(self):
        for arm in self._robot_dict.values():
            arm.connect()
        for hand in self._hand_dict.values():
            hand.connect()

    def close(self):
        ...

    def get_robot_handle(self) -> Dict[str, RobotArmDriver]:
        return self._robot_dict

    def get_camera_handle(self) -> Dict[str, CameraDriver]:
        return self._camera_dict

    def get_gripper_handle(self) -> Dict[str, HandDriver]:
        return self._hand_dict

    def step(self, action: Dict[str, dict]) -> Dict[str, Any]:
        if 'arm_joints' in action:
            for name, joints in action['arm_joints'].items():
                if name in self._robot_dict:
                    self._robot_dict[name].sync_target_joint(joints)
        if 'hand_joints' in action:
            for name, joints in action['hand_joints'].items():
                if name in self._hand_dict:
                    self._hand_dict[name].sync_target_joint(joints)
        self._rate.sleep()
        return self.get_obs()

    def step_ee(self, robot_targets: Dict[str, dict]) -> Dict[str, Any]:
        if 'arm_pose' in robot_targets:
            for name, joints in robot_targets['arm_pose'].items():
                self._robot_dict[name].sync_target_joint(joints)
        if 'hand_joints' in robot_targets:
            for name, joints in robot_targets['hand_joints'].items():
                self._hand_dict[name].sync_target_joint(joints)
        self._rate.sleep()
        return self.get_obs()

    def get_robot_state(self) -> Dict[str, Any]:
        observations = {
            "arm_joints":
                {name: arm.get_current_joint().get_radian_ndarray() for name, arm in self._robot_dict.items()},
            "arm_pose":
                {name: arm.get_tool_cartesian_pose().get_xyz_m_xyzw_ndarray() for name, arm in self._robot_dict.items()},
            "hand_joints":
                {name: hand.get_current_joint().get_radian_ndarray() for name, hand in self._hand_dict.items()}
        }
        return observations

    def get_obs(self) -> Dict[str, Any]:
        images = {}
        depths = {}
        for name, camera in self._camera_dict.items():
            image, depth = camera.read()
            images[name] = image
            depths[name] = depth
        observations = self.get_robot_state()
        observations["images"] = images
        observations["depths"] = depths
        return observations

    @abstractmethod
    def decompose_action(self, action: np.ndarray) -> dict:
        ...