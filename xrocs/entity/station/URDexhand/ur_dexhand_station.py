import threading
from typing import Any, Dict, <PERSON><PERSON>
import time
import numpy as np
from xrocs.entity.camera.orbbec_camera import OrbbecClientCamera
from xrocs.entity.hand.Dexhand.dexhand import X_Dexhand
from xrocs.entity.robot.UR.ur import URRobot
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.utils.logger.logger_loader import logger

from pyzlg_dexhand.dexhand_interface import (
    LeftDexHand,
    RightDexHand,
    ControlMode,
    ZCANWrapper,
    HandFeedback,
)

class URDexhandStation(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(URDexhandStation, cls).__new__(cls)
        return URDexhandStation._instance

    def __init__(self, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        self.observations = {}
        # "left","right"
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = URRobot(robot_ip=ip)

        # "front", "left", "right", ...
        self._camera_dict = {}
        for name, value in camera_dict.items():
            self._camera_dict[name] = OrbbecClientCamera(value)

        # "left","right"
        self._hand_dict = {}
        zcan = ZCANWrapper()
        if not zcan.open():
            raise RuntimeError("Failed to open ZCAN device")
        for name, _ in hand_dict.items():
            print("hand_name:::", name)
            self._hand_dict[name] = X_Dexhand(name, zcan)


        self.control_rate_hz = 100
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)

        self._running = False
        self.start()
        print("\nStart 🚀🚀🚀")

    def start(self):
        if not self._running:
            self._running = True
            self.left_felling_thread = threading.Thread(
                target=self._update_left_loop,
                daemon=True)
            self.right_felling_thread = threading.Thread(
                target=self._update_right_loop,
                daemon=True)
            self.threads = [self.left_felling_thread, self.right_felling_thread]
            # 启动线程
            for thread in self.threads:
                thread.start()

    def _update_left_loop(self):
        time.sleep(2.5)
        log_cnt_left = 0
        while self._running:
            try:
                self.left_hand_joint = {"left": self._hand_dict["left"].get_current_joint().get_radian_ndarray()}
                self.left_hand_felling = {"left": self._hand_dict["left"].get_feeling()}
            except Exception as e:
                if log_cnt_left % 10 == 0:
                    # Prevent logs from being too fast
                    logger.error(f"Error updating left_felling: {e}")
                    log_cnt_left = 0 
                log_cnt_left += 1

    def _update_right_loop(self):
        time.sleep(2.5)
        log_cnt_right = 0
        while self._running:
            try:
                self.right_hand_joint = {"right": self._hand_dict["right"].get_current_joint().get_radian_ndarray()}
                self.right_hand_felling = {"right": self._hand_dict["right"].get_feeling()}
            except Exception as e:
                if log_cnt_right % 10 == 0:
                    # Prevent logs from being too fast
                    logger.error(f"Error updating right_felling: {e}")
                    log_cnt_right = 0
                log_cnt_right += 1

    def get_robot_state(self) -> Dict[str, Any]:
        observations = {
            "arm_joints":
                {name: arm.get_current_joint().get_radian_ndarray() for name, arm in self._robot_dict.items()},
            "arm_pose":
                {name: arm.get_tool_cartesian_pose().get_xyz_m_rpy_radian_ndarray() for name, arm in self._robot_dict.items()},
            "hand_joints":
                {**self.left_hand_joint, **self.right_hand_joint},
            "hand_feeling":
                {**self.left_hand_felling, **self.right_hand_felling}
        }
        return observations
    
    def get_obs(self) -> Dict[str, Any]:
        observations = self.get_robot_state()
        images = {}
        depths = {}
        for name, camera in self._camera_dict.items():
            image, depth = camera.read()
            images[name] = image
            depths[name] = depth
        observations["images"] = images
        observations["depths"] = depths
        return observations
    
    def step(self, action: Dict[str, dict]) -> Dict[str, Any]:
        for name, joints in action['arm_joints'].items():
            self._robot_dict[name].sync_target_joint(joints)
        for name, joints in action['hand_joints'].items():
            self._hand_dict[name].sync_target_joint(joints)
        self._rate.sleep()
        return self.get_obs()

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "left": action[0:6],
                "right": action[18:24],
            },
            "hand_joints": {
                "left": action[6:18],
                "right": action[24:36],
            }
        }
   
    def stop(self):
        self._running = False
        if self._thread is not None:
            for thread in self.threads:
                thread.join()

    def __del__(self):
        self.stop()