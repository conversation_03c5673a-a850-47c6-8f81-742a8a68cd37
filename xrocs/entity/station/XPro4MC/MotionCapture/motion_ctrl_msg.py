import time
from typing import Optional
import numpy as np
import rospy
from sensor_msgs.msg import JointState

class TienKungCtrlMsg():
    def __init__(self, robot_ip: str, local_ip: str):
        rospy.Subscriber('/human_arm_ctrl_left', JointState, self._left_arm_callback)
        rospy.Subscriber('/human_arm_ctrl_right', JointState, self._right_arm_callback)
        rospy.Subscriber('/inspire_hand/ctrl/left_hand', JointState, self._left_hand_callback)
        rospy.Subscriber('/inspire_hand/ctrl/right_hand', JointState, self._right_hand_callback)
        self.left_jpos = None
        self.right_jpos = None
        self.left_hand = None
        self.right_hand = None
        time.sleep(2)
        self.rate = rospy.Rate(400)

    def _left_arm_callback(self, data):
        self.left_jpos = list(data.position)

    def _right_arm_callback(self, data):
        self.right_jpos = list(data.position)

    def _left_hand_callback(self, data):
        self.left_hand = list(data.position)

    def _right_hand_callback(self, data):
        self.right_hand = list(data.position)

    def act(self) -> Optional[list]:
        return np.concatenate([self.left_jpos, self.left_hand, self.right_jpos, self.right_hand]).tolist()