import threading
import os
import numpy as np
import rospy
from typing import Optional, Dict, Any, Tuple
from xrocs.common.logger_loader import logger
from xrocs.entity.camera.tienkung_camera import TienKungCamera
from xrocs.entity.hand.InspireHand.inspire_hand import InspiredHand
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.robot.XRobot.tienkung_pro import TienkungRobot
from xrocs.entity.station.XPro4MC.MotionCapture.motion_ctrl_msg import TienKungCtrlMsg

os.environ["ROS_MASTER_URI"] = "http://************:11311"
os.environ["ROS_IP"] = "************"

class XProDHand(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, 
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str],
                is_intrp: bool
                ):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(XProDHand, cls).__new__(cls)
        return XProDHand._instance

    def __init__(self,
                 robot_dict: dict[str, str],
                 camera_dict: dict[str, str],
                 hand_dict: dict[str, str],
                 is_intrp: bool = False
                 ):
        
        logger.success("TienkungRobot Node")
        rospy.init_node('TienkungRobotNode')
        logger.success("TienkungRobot Node Initialized")

        self.is_infer = False
        self.m_tele = TienKungCtrlMsg()

        local_ip = '************'
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = TienkungRobot(robot_ip=ip, local_ip=local_ip, is_intrp=is_intrp)
        self._camera_dict = {}
        for name, cam_name in camera_dict.items(): 
            self._camera_dict[name] = TienKungCamera(cam_name)
        self._hand_dict = {}
        for name, robot_ip in hand_dict.items():
            self._hand_dict[name] = InspiredHand(hand_ip=robot_ip, local_ip=local_ip, hand_type=name)
        self.control_rate_hz = 100
        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        tmp_action = self.m_tele.act()
        return {
            "arm_joints": {
                "robot": np.concatenate([tmp_action[0:7], tmp_action[13:20]])
            },
            "hand_joints": {
                "left": np.asarray([tmp_action[7:13]]),
                "right": np.asarray([tmp_action[20:26]])
            }
        }
    
    def step(self, 
             robot_joints: Dict[str, dict],
             hand_joints: Dict[str, dict]) -> Dict[str, Any]:
        if self.is_infer:
            for name, joints in robot_joints['arm_joints'].items():
                self._robot_dict[name].sync_target_joint(joints)
            for name, joints in hand_joints['hand_joints'].items():
                self._hand_dict[name].sync_target_joint(joints)            
        else:
            pass
        self._rate.sleep()
        return self.get_obs()

