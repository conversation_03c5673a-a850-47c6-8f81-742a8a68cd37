import threading
from typing import <PERSON><PERSON>

import numpy as np

from xrocs.entity.camera.mock_camera import <PERSON>ckCamera
from xrocs.entity.hand.MockHand.mock_hand import HockHand
from xrocs.entity.robot.MockRobot.mock_env import <PERSON>ckRobot
from xrocs.entity.station.robot_env import RobotEnvBase


class MockStation(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(MockStation, cls).__new__(cls)
        return MockStation._instance

    def __init__(self, robot_dict: dict[str, str], camera_dict: dict[str, str], hand_dict: dict[str, str]):
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = MockRobot(robot_ip=ip)
        self._camera_dict = {}
        for name, port in camera_dict.items():
            self._camera_dict[name] = MockCamera(port=port)
        self._hand_dict = {}
        for name, robot_ip in hand_dict.items():
            self._hand_dict[name] = HockHand(robot_ip=robot_ip)
        self.control_rate_hz = 100

        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "robot": action[0:7],
            },
            "hand_joints": {
                "robot": [action[7]],
            }
        }
