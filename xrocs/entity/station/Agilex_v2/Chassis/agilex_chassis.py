import time
import rospy
import tqdm
import numpy as np
from typing import Optional, Dict, Any
from std_msgs.msg import Head<PERSON>
from sensor_msgs.msg import JointState
from geometry_msgs.msg import Twist
from nav_msgs.msg import Odometry
from xrocs.common.logger_loader import logger

class AgilexChassis():
    def __init__(self) -> None:
        rospy.Subscriber('/odom', Odometry, self._chassis_callback, queue_size=10)
        self.chassis_publisher = rospy.Publisher('/cmd_vel', Twist, queue_size=10)

        self._m_odom = Odometry()
        self.rate = rospy.Rate(100)
        self.seq = 0
        time.sleep(1)
    
    def _chassis_callback(self, data):
        self._m_odom = data
        pass

    def _convert_pose_to_array(self, pose) -> np.ndarray:
        position = [
            pose.pose.position.x,
            pose.pose.position.y,
            pose.pose.position.z
        ]
        orientation = [
            pose.pose.orientation.x,
            pose.pose.orientation.y,
            pose.pose.orientation.z,
            pose.pose.orientation.w
        ]
        pose_data = np.array(position + orientation)  # 7
        covariance = np.array(pose.covariance)        # 36
        return pose_data, covariance
    
    def _convert_twist_to_array(self, twist) -> np.ndarray:
        linear = [
            twist.twist.linear.x,
            twist.twist.linear.y,
            twist.twist.linear.z
        ]
        angular = [
            twist.twist.angular.x,
            twist.twist.angular.y,
            twist.twist.angular.z
        ]
        twist_data = np.array(linear + angular)       # 6
        covariance = np.array(twist.covariance)       # 36
        return twist_data, covariance
    
    def get_chassis_state(self) -> Dict[str, Any]:
        t_pose, t_pose_cov = self._convert_pose_to_array(self._m_odom.pose)
        t_twist, t_twist_cov = self._convert_twist_to_array(self._m_odom.twist)
        observations = {
            "chassis_pose": t_pose,
            "chassis_pose_cov": t_pose_cov,
            "chassis_twist": t_twist,
            "chassis_twist_cov": t_twist_cov
        }
        return observations
    
    def sync_target_twist(self, target_twist: Twist) -> None:
        self.chassis_publisher.publish(target_twist)