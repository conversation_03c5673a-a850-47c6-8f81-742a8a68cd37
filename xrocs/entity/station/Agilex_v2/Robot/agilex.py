from typing import Optional, List
import numpy as np
import rospy 

from xrocs.common.data_type import Joints
from xrocs.common.logger_loader import logger
from xrocs.entity.robot.robot_base import RobotArmDriver
from sensor_msgs.msg import JointState
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Header
from xrocs.common.data_type import Pose, Rotation, Coordinate


class AgilexRobot(RobotArmDriver):
    def __init__(self, robot_type: str) -> None:
        self.robot_type = robot_type
        if robot_type == 'left':
            self.puppet_arm_publisher = rospy.Publisher('/master/joint_left', JointState, queue_size=10)
            rospy.Subscriber('/puppet/joint_left', JointState, self._puppet_arm_callback, queue_size=10)
            rospy.Subscriber('/master/end_left', PoseStamped, self._puppet_pose_callback, queue_size=10)
        elif robot_type == 'right':
            self.puppet_arm_publisher = rospy.Publisher('/master/joint_right', JointState, queue_size=10)
            rospy.Subscriber('/puppet/joint_right', JointState, self._puppet_arm_callback, queue_size=10)
            rospy.Subscriber('/master/end_right', PoseStamped, self._puppet_pose_callback, queue_size=10)
        self.rate = rospy.Rate(100)
        self.joint_state = None
        self.end_coor = Coordinate([0,0,0])
        self.end_rot = Rotation([0,0,0,0], rotation_form_type=Rotation.FormType.XYZW)
        
    def num_dofs(self) -> int:
        return 7

    def connect(self) -> bool:
        return True

    def get_current_joint(self) -> Optional[Joints]:
        return Joints(self.joint_state, len(self.joint_state))

    def reach_target_joint(self, target_joint: Joints) -> bool:
        fine_step = 100
        step_array = np.linspace(self.get_current_joint().get_radian_ndarray(),
                                 target_joint.get_radian_ndarray(), fine_step)
        for stp in step_array:
            self.sync_target_joint(stp)
            self.rate.sleep()
        return True
    
    def _construct_target_msg(self, target_joint: List[float]) -> JointState:
        joint_state_msg = JointState()
        joint_state_msg.header = Header()
        joint_state_msg.header.stamp = rospy.Time.now()  # 设置时间戳
        joint_state_msg.name = ['joint0', 'joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']  # 设置关节名称
        joint_state_msg.position = target_joint
        return joint_state_msg

    def sync_target_joint(self, target_joint: np.ndarray) -> None:
        self.puppet_arm_publisher.publish(self._construct_target_msg(target_joint.tolist()))
        return

    def _puppet_arm_callback(self, data):
        self.joint_state = list(data.position)

    def _puppet_pose_callback(self, data):
        self.end_coor, self.end_rot = self._decode_pose_msg(data)
    
    def _decode_pose_msg(self, pose):
        position = [
            pose.position.x,
            pose.position.y,
            pose.position.z
        ]
        orientation = [
            pose.orientation.x,
            pose.orientation.y,
            pose.orientation.z,
            pose.orientation.w
        ]
        return Coordinate(list(position)), Rotation(list(orientation), rotation_form_type=Rotation.FormType.XYZW)

    def get_tool_cartesian_pose(self):
        return Pose(self.end_coor, self.end_rot)
    

    # To do
    def reach_tool_cartesian_pose(self):
        pass

    def sync_tool_cartesian_pose(self):
        pass