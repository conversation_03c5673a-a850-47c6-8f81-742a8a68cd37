import numpy as np
import rospy 
from std_msgs.msg import Head<PERSON>
from geometry_msgs.msg import Twist
from sensor_msgs.msg import JointState, Image 
from nav_msgs.msg import Odometry
from xrocs.common.logger_loader import logger

class AgilexTeleoperator:
    def __init__(self):
        rospy.Subscriber('/master/joint_left', JointState, self.tele_callback_left, queue_size=10)
        rospy.Subscriber('/master/joint_right', JointState, self.tele_callback_right, queue_size=10)
        self.tele_joints_left = None
        self.tele_joints_right = None
        logger.success("Agilex teleoperator node")
    
    def tele_callback_left(self, data):
        self.tele_joints_left = np.array(data.position)

    def tele_callback_right(self, data):
        self.tele_joints_right = np.array(data.position)

    def act(self):
        return np.concatenate((self.tele_joints_left, self.tele_joints_right))
    

if __name__ == "_main__":
    aa = AgilexTeleoperator()