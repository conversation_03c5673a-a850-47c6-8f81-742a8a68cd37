import threading
import numpy as np
import rclpy
from rclpy.node import Node

from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.robot.XRobot.tienyi_2 import TienYi2Robot


class XTY2Arm(RobotEnvBase):
    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls,
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str]):
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(XTY2Arm, cls).__new__(cls)
        return XTY2Arm._instance

    def __init__(self, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        
        logger.success("Tienyi2Robot Node")
        rclpy.init()
        logger.success("Tienyi2Robot Node Initialized")

        self.node = Node("tienyi2_node")

        self._spin_thread = threading.Thread(target=rclpy.spin, args=(self.node,), daemon=True)
        self._spin_thread.start()

        local_ip = '127.0.0.1'
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = TienYi2Robot(node=self.node, robot_ip=ip, local_ip=local_ip)
        self._camera_dict = {}
        self._hand_dict = {}
        self.control_rate_hz = 100

        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "robot": np.concatenate([action[0:7], action[8:15]])
            },
            "hand_joints": {
                "left": np.asarray([action[7]]),
                "right": np.asarray([action[15]])
            }
        }
