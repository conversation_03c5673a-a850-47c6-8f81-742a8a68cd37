import threading
import numpy as np
from xrocs.entity.camera.orbbec_camera import OrbbecClientCamera
from xrocs.entity.robot.Franka.franka import FrankaRobot
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.hand.RobotiqFranka.robotiq_gripper import RobotiqGripperFranka


class FrankaStdStation(RobotEnvBase):

    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls,
                robot_dict: dict[str, str],
                camera_dict: dict[str, str],
                hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(FrankaStdStation, cls).__new__(cls)
        return FrankaStdStation._instance

    def __init__(self, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = <PERSON><PERSON><PERSON><PERSON><PERSON>(robot_ip=ip, arm_type=name)
        self._camera_dict = {}
        for name, value in camera_dict.items():
            self._camera_dict[name] = OrbbecClientCamera(value)
        self._hand_dict = {}
        for name, port in hand_dict.items():
            self._hand_dict[name] = RobotiqGripperFranka(self._robot_dict[name])
        self.control_rate_hz = 100

        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
            "arm_joints": {
                "left": action[0:8],
                "right": action[8:16],
            },
            "hand_joints": {
                "left": np.asarray([action[7]]),
                "right": np.asarray([action[15]]),
            }
        }
