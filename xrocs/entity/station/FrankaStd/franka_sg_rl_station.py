import threading
from typing import Dict, <PERSON><PERSON>

import numpy as np

from xrocs.core.Communication.camera_node import ZMQClientCamera
from xrocs.entity.robot.Franka.franka import <PERSON><PERSON><PERSON><PERSON>ot
from xrocs.entity.station.robot_env import RobotEnvBase
from xrocs.entity.hand.RobotiqFranka.robotiq_gripper import RobotiqGripperFranka


class FrankaRlStation(RobotEnvBase):

    _single_lock = threading.Lock()
    _instance = None

    def __new__(cls, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        # single instance
        with cls._single_lock:
            if cls._instance is None:
                cls._instance = super(FrankaRlStation, cls).__new__(cls)
        return FrankaRlStation._instance

    def __init__(self, robot_dict: dict[str, str],
                 camera_dict: dict[str, str], hand_dict: dict[str, str]):
        self._robot_dict = {}
        for name, ip in robot_dict.items():
            self._robot_dict[name] = Franka<PERSON>obot(robot_ip=ip, arm_type=name)
        self._camera_dict = {}
        # for name, port in camera_dict.items():
            # self._camera_dict[name] = ZMQClientCamera(port=port)
        for name, port in camera_dict.items():
            self._camera_dict[name] = ZMQClientCamera(port=port['port'])
        self._hand_dict = {}
        for name, port in hand_dict.items():
            self._hand_dict[name] = RobotiqGripperFranka(self._robot_dict[name])
        self.control_rate_hz = 100

        super().__init__(self._robot_dict, self._camera_dict, self._hand_dict, self.control_rate_hz)
        print("\nStart 🚀🚀🚀")

    def assemble_station(self, action_dict: dict) -> Tuple[dict, dict]:
        robot_targets, _ = super().assemble_station(action_dict)
        hand_targets = {'hand_joints': {}}
        return robot_targets, hand_targets
    
    def step(self, robot_joints: Dict[str, dict],
             hand_joints: Dict[str, dict]) -> Dict[str, dict]:
        for name, joints in robot_joints['arm_joints'].items():
            gp = self._hand_dict[name].get_current_joint().get_radian_ndarray()[0]
            if isinstance(joints,list):
                _joints = joints + [gp]
            else:
                _joints = joints.tolist() + [gp]
            self._robot_dict[name].sync_target_joint(np.asarray(_joints))
        for name, joints in hand_joints['hand_joints'].items():
            self._hand_dict[name].sync_target_joint(joints)
        self._rate.sleep()
        return self.get_obs()

    def decompose_action(self, action: np.ndarray) -> dict:
        return {
                "arm_joints": {
                    "robot": action[0:8],
                },
                "hand_joints": {
                    "robot": np.asarray([action[7]]),
                }
            }

    def get_ee_pose_from_joint(self, joints) -> dict:
        for name in self._robot_dict:
            ee_pose = self._robot_dict[name].get_ee_pose_from_joint(joints)
        return ee_pose
