from typing import Optional, <PERSON><PERSON>
import numpy as np
import cv2
from xrocs.utils.logger.logger_loader import logger
from xrocs.entity.camera.camera_base import CameraDriver


class MockCamera(CameraDriver):
    """A dummy camera for testing."""

    def __init__(self, port: str) -> None:
        logger.success(f"Connect to camera at port {port}")

    def read(self,
             image_size: Optional[Tuple[int, int]] = None) -> Tuple[np.ndarray, np.ndarray]:
        rgb, depth = self._read(image_size)
        _, rgb_image = cv2.imencode(".jpg", rgb)
        depth_uint16 = depth.clip(0., 65535).astype(np.uint16)
        _, depth_image = cv2.imencode(".png", depth_uint16)
        return rgb_image, depth_image

    def _read(
        self,
        img_size: Optional[Tuple[int, int]] = None,
    ) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """Read a frame from the camera.

        Args:
            img_size: The size of the image to return. If None, the original size is returned.

        Returns:
            np.ndarray: The color image.
            np.ndarray: The depth image.
        """
        if img_size is None:
            return (
                np.random.randint(255, size=(480, 640, 3), dtype=np.uint8),
                np.random.randint(255, size=(480, 640, 1), dtype=np.uint16),
            )
        else:
            return (
                np.random.randint(
                    255, size=(img_size[0], img_size[1], 3), dtype=np.uint8
                ),
                np.random.randint(
                    255, size=(img_size[0], img_size[1], 1), dtype=np.uint16
                ),
            )