import numpy as np
import rospy
from sensor_msgs.msg import Image
from typing import Op<PERSON>, Tu<PERSON>
from message_filters import Subscriber, ApproximateTimeSynchronizer
import cv_bridge
import cv2
from xrocs.entity.camera.camera_base import CameraDriver


class TienKungCamera(CameraDriver):
    def __init__(self, camera_name: str):
        rgb_sub = Subscriber(f'/{camera_name}/color/image_raw', Image)
        depth_sub = Subscriber(f'/{camera_name}/depth/image_raw', Image)
        ats = ApproximateTimeSynchronizer(
            [rgb_sub, depth_sub],
            queue_size=10,
            slop=0.1,
            allow_headerless=False
        )
        ats.registerCallback(self.image_callback)
        self.bridge = cv_bridge.CvBridge()

        self.image_rgb = None
        self.image_depth = None

    def image_callback(self, rgb_msg, depth_msg):
        try:
            tmp_rgb = self.bridge.imgmsg_to_cv2(rgb_msg, desired_encoding='passthrough')
            _, self.image_rgb = cv2.imencode(".jpg", tmp_rgb)
            tmp_depth = self.bridge.imgmsg_to_cv2(depth_msg, desired_encoding='passthrough')
            depth_uint16 = tmp_depth.clip(0., 65535).astype(np.uint16)
            _, self.image_depth = cv2.imencode(".png", depth_uint16)

        except Exception as e:
            rospy.logerr("Error converting images: %s", e)

    def read(
        self,
        img_size: Optional[Tuple[int, int]] = None,
    ) -> Tuple[np.ndarray, np.ndarray]:
        return self.image_rgb, self.image_depth
