from typing import Optional, <PERSON><PERSON>
import numpy as np
import zerorpc
import cv2
import time
import os
import json
from xrocs.entity.camera.camera_base import CameraDriver


class OrbbecClientCamera(CameraDriver):
    def __init__(self, import_config):
        host = "0.0.0.0"
        port = import_config["port"]
        self.cam = zerorpc.Client()
        self.cam.connect(f"tcp://{host}:{port}")
        self.port = port
        self.cfg_dict = import_config

    def read(self) -> Tuple[np.ndarray, np.ndarray]:
        rgb_bytes, depth_bytes = self.cam.get_latest_images()
        size = tuple(self._cfg_dict["size"])
        rgb = np.frombuffer(rgb_bytes, dtype=np.uint8).reshape(size[0], size[1], 3)
        depth = np.frombuffer(depth_bytes, dtype=np.float64).reshape(size[0], size[1])
        depth_uint16 = depth.clip(0., 65535).astype(np.uint16)
        _, rgb_image = cv2.imencode(".jpg", rgb)
        _, depth_image = cv2.imencode(".png", depth_uint16)
        return rgb_image, depth_image
    
    @staticmethod
    def _process_intrinsics(params):
        intrinsics = {"cameraMatrix": [[params.fx, 0, params.ppx], [0, params.fy, params.ppy], [0, 0, 1]],
                      "distCoeffs": list(params.coeffs)}
        return intrinsics
    
    def read_intrinsics(self) -> dict:
        intrinsics_dict = {}
        # read intrinsics
        calibration_dir = os.path.expanduser("~/Dev/sysEAI/xRocs/xrocs/entity/calibration/")
        # find target json file
        for json_file in os.listdir(calibration_dir):
            file_path = os.path.join(calibration_dir, json_file)
            # read JSON file
            with open(file_path, "r") as f:
                intrinsics_data = json.load(f)
                intrinsics_dict.update(intrinsics_data)
        return intrinsics_dict
    
    def read_parameter(self, calibration_dir:str = os.path.expanduser("~/Dev/sysEAI/xRocs/xrocs/entity/calibration/")) -> dict:
        """Read camera intrinsics and extrinsics.

        Args:
            calibration_dir: The path of the parameter.

        Returns:
            dict: A nested dictionary containing calibration parameters for each camera, structured as:
                {
                    "<camera_name>": {
                        "pose": np.ndarray,        # 6D extrinsic pose [x, y, z, rx, ry, rz] in meters/radians
                        "cameraMatrix": np.ndarray, # 3x3 intrinsic matrix [[fx, 0, cx], [0, fy, cy], [0, 0, 1]]
                        "distCoeffs": np.ndarray,   # Distortion coefficients [k1, k2, p1, p2, k3]
                        "timestamp": float,         # UNIX timestamp of calibration
                        "serial": str               # Camera serial number
                    },
                    ...
                }
        """
        # read intrinsics
        parameter_dict = {}
        # find target json file
        for json_file in os.listdir(calibration_dir):
            if json_file.endswith('.json'):  # Only read .json
                file_path = os.path.join(calibration_dir, json_file)
                # read JSON file
                with open(file_path, "r") as f:
                    parameter_data = json.load(f)
                    parameter_dict.update(parameter_data)
        return parameter_dict
    

if __name__ == "__init__":
    aa = OrbbecClientCamera()
    aa.read()

elif __name__ == "__main__":
    aa = OrbbecClientCamera()
    camera_data = aa.read()
    print(camera_data)

    import pyorbbecsdk
    pipeline = pyorbbecsdk.Pipeline()
    config = pyorbbecsdk.Config()
    pipeline.start(config)
    intrinsic = pipeline.get_camera_intrinsic(pyorbbecsdk.OB_STREAM_COLOR)
    print(f"焦距 (fx, fy): {intrinsic.fx}, {intrinsic.fy}")
    print(f"主点 (cx, cy): {intrinsic.cx}, {intrinsic.cy}")
    print(f"畸变系数: {intrinsic.distortion_coeffs}")