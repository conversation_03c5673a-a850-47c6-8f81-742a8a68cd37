import os
import time
from typing import List, Optional, Tuple
from pathlib import Path
import json
import cv2
import numpy as np
from tqdm import tqdm
import pyrealsense2 as rs
from xrocs.entity.camera.camera_base import CameraDriver

def get_device_ids() -> List[str]:
    ctx = rs.context()
    devices = ctx.query_devices()
    device_ids = []
    for dev in devices:
        device_ids.append(dev.get_info(rs.camera_info.serial_number))
    for _ in tqdm(range(3), desc="wait cameras up"):
        time.sleep(1)
    return device_ids

class RealSenseCamera(CameraDriver):
    def __repr__(self) -> str:
        return f"RealSenseCamera(device_id={self._device_id})"

    def __init__(self, import_config: dict):
        camera_data_size = import_config["size"]
        self._device_id = import_config["serial"]
        freq = import_config["freq"]
        self._pipeline = rs.pipeline()
        config = rs.config()
        config.enable_device(str(self._device_id))
        config.enable_stream(rs.stream.depth, camera_data_size[0], camera_data_size[1], rs.format.z16, freq)
        config.enable_stream(rs.stream.color, camera_data_size[0], camera_data_size[1], rs.format.bgr8, freq)
        align_to = rs.stream.color
        self._aligned_fs = rs.align(align_to)
        self.cfg = self._pipeline.start(config)
        print("camera_init finished:", self._device_id)
    
    def save_intrin(self):
        profile = self.cfg.get_stream(rs.stream.color) # Fetch stream profile for depth stream
        color_intrin = profile.as_video_stream_profile().get_intrinsics() 
        _intrinsics = {
            self._device_id: self._process_intrinsics(color_intrin),
        }
        
        # Create the full path to the calibration file
        calibration_dir = Path(__file__).parent.parent / "calibration"
        calibration_file = calibration_dir / f"{self._device_id}_intrinsics.json"
        
        # Create directory if it doesn't exist
        calibration_dir.mkdir(parents=True, exist_ok=True)
        
        # Write the file
        with open(calibration_file, "w+") as jsonFile:
            json.dump(_intrinsics, jsonFile)
        print(f"Camera {self._device_id} intrinsics saved to {calibration_file}")

    @staticmethod
    def _process_intrinsics(params):
        intrinsics = {"cameraMatrix": [[params.fx, 0, params.ppx], [0, params.fy, params.ppy], [0, 0, 1]],
                      "distCoeffs": list(params.coeffs)}
        return intrinsics

    def read(
        self,
        img_size: Optional[Tuple[int, int]] = None,  # farthest: float = 0.12
    ) -> Tuple[np.ndarray, np.ndarray]:
        """Read a frame from the camera.

        Args:
            img_size: The size of the image to return. If None, the original size is returned.
            farthest: The farthest distance to map to 255.

        Returns:
            np.ndarray: The color image, shape=(H, W, 3)
            np.ndarray: The depth image, shape=(H, W, 1)
        """

        frames = self._pipeline.wait_for_frames()
        frames = self._aligned_fs.process(frames)
        color_frame = frames.get_color_frame()
        color_image = np.asanyarray(color_frame.get_data())
        depth_frame = frames.get_depth_frame()
        depth_image = np.asanyarray(depth_frame.get_data())
        if img_size is None:
            image = color_image
            depth = depth_image
        else:
            image = cv2.resize(color_image, img_size)
            depth = cv2.resize(depth_image, img_size)

        depth_uint16 = depth.astype(np.uint16)
        image = cv2.imencode(".jpg", image)[1]
        depth = cv2.imencode(".png", depth_uint16)[1]
        return image, depth
    
    def read_parameter(self, calibration_dir:str = os.path.expanduser("~/Dev/sysEAI/xRocs/xrocs/entity/calibration/")) -> dict:
        """Read camera intrinsics and extrinsics.

        Args:
            calibration_dir: The path of the parameter.Default: ~/Dev/sysEAI/xRocs/xrocs/entity/calibration/

        Returns:
            dict: A nested dictionary containing calibration parameters for each camera, structured as:
                {
                    "<camera_name>": {
                        "pose": np.ndarray,        # 6D extrinsic pose [x, y, z, rx, ry, rz] in meters/radians
                        "cameraMatrix": np.ndarray, # 3x3 intrinsic matrix [[fx, 0, cx], [0, fy, cy], [0, 0, 1]]
                        "distCoeffs": np.ndarray,   # Distortion coefficients [k1, k2, p1, p2, k3]
                        "timestamp": float,         # UNIX timestamp of calibration
                        "serial": str               # Camera serial number
                    },
                    ...
                }
        """
        # read intrinsics
        parameter_dict = {}
        # find target json file
        for json_file in os.listdir(calibration_dir):
            if json_file.endswith('.json'):  # Only read .json
                file_path = os.path.join(calibration_dir, json_file)
                # read JSON file
                with open(file_path, "r") as f:
                    parameter_data = json.load(f)
                    parameter_dict.update(parameter_data)
        return parameter_dict

if __name__ == "__main__":
    device_ids = get_device_ids()
    print(f"Found {len(device_ids)} devices: {device_ids}")
    for dv_id in device_ids:
        config = {}
        config["serial"] = dv_id
        config["size"] = [640, 480]
        config["freq"] = 30
        t_camera = RealSenseCamera(config)
        t_camera.save_intrin()
