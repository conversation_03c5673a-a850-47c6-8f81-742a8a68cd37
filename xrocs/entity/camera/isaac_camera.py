from typing import Optional, <PERSON><PERSON>
import time
import numpy as np
import zerorpc
import cv2
from xrocs.entity.camera.camera_base import CameraDriver


class IsaacCamera(CameraDriver):
    def __init__(self, robot_driver, cam_name):
        self._cam_name = cam_name
        self._robot_driver = robot_driver

    def _read(
                self,
                img_size: Optional[Tuple[int, int]] = None,
        ) -> Tuple[np.ndarray, np.ndarray]:
            size = (720, 1280) if self._cam_name == 'top' else (480, 640)
            rgb_bytes, depth_bytes = self._robot_driver.get_driver().get_image(self._cam_name)

            rgb = np.frombuffer(rgb_bytes, dtype=np.uint8).reshape(size[0], size[1], 3)
            depth_image = np.frombuffer(depth_bytes, dtype=np.float32).reshape(size[0], size[1]).copy()

            _, rgb_image = cv2.imencode(".jpg", rgb)

            depth_image[np.where(depth_image > 6.5)] = 0.0
            depth_image_65 = depth_image * 1000
            depth_image_uint16 = depth_image_65.astype(np.uint16)
            depth_image_uint16_encoded = cv2.imencode(".png", depth_image_uint16)[1]

            return rgb_image, depth_image_uint16_encoded

    def read(
            self,
            img_size: Optional[Tuple[int, int]] = None,
            timeout: float = 30.0
    ) -> Tuple[np.ndarray, np.ndarray]:
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                return self._read(img_size)
            except TypeError as e:
                print("Encountered error:", e, "— retrying after delay.")
                time.sleep(0.001)  

        raise TimeoutError("Failed to read images within the allowed time frame.")


if __name__ == "__main__":
    class Driver:
        def __init__(self):
            self.isaac_driver = zerorpc.Client(heartbeat=20)
            self.isaac_driver.connect("tcp://" + "0.0.0.0" + f":4245")
        def get_driver(self):
            return self.isaac_driver
        
    driver = Driver()

    val = ['left', 'right', 'top', 'handeye']
    for v in val:
        aa = IsaacCamera(driver, cam_name=v)
        rgb, depth = aa.read()
        rgb.tofile(f"/home/<USER>/code_repo/sysEAI/rgb_{v}.jpg")
        depth.tofile(f"/home/<USER>/code_repo/sysEAI/depth_{v}.png")