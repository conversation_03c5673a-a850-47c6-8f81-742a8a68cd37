import cv2
import numpy as np
np.set_printoptions(suppress=True)
from copy import deepcopy
from xrocs.utils.logger.logger_loader import logger
from xrocs.core.app_base import RunnerBase
from xrocs.core.station_loader import StationLoader
from xrocs.core.config_loader import ConfigLoader
from xtele.core.integrate_module import TeleCore
from xrocs.utils.Calibration.calibration_utils import HandCameraCalibrator, ThirdPersonCameraCalibrator, \
    update_calibration_info, set_calib_info_path
import json
import os


class StationCali:
    def __init__(self, config_path : str = None):
        cfg_loader = ConfigLoader(config_path)
        self.cfg_dict = cfg_loader.get_config()
        station_loader = StationLoader(self.cfg_dict)
        self.robot_station = station_loader.generate_station_handle()
        self.tele_agent = TeleCore()
        self.runner = RunnerBase(self.tele_agent, self.robot_station)
        self.robot_station.connect()
        self.episode_length = 20000
        self.max_episode = 2000

    def decoder_image(self, camera_rgb_images, camera_depth_images, bgr2rgb=False):
        if type(camera_rgb_images[0]) is np.uint8:
            rgb = cv2.imdecode(camera_rgb_images, cv2.IMREAD_COLOR)
            if camera_depth_images is not None:
                depth_array = np.frombuffer(camera_depth_images, dtype=np.uint8)
                depth = cv2.imdecode(depth_array, cv2.IMREAD_UNCHANGED)
            else:
                depth = np.asarray([])
            return rgb, depth
        else:
            rgb_images = []
            depth_images = []
            for idx, camera_rgb_image in enumerate(camera_rgb_images):
                rgb = cv2.imdecode(camera_rgb_image, cv2.IMREAD_COLOR)
                if camera_depth_images is not None:
                    depth_array = np.frombuffer(camera_depth_images[idx], dtype=np.uint8)
                    depth = cv2.imdecode(depth_array, cv2.IMREAD_UNCHANGED)
                else:
                    depth = np.asarray([])
                if bgr2rgb and rgb is not None:
                    rgb = cv2.cvtColor(rgb, cv2.COLOR_BGR2RGB)
                rgb_images.append(rgb)
                depth_images.append(depth)
            rgb_images = np.asarray(rgb_images)
            depth_images = np.asarray(depth_images)
            return rgb_images, depth_images

    def prepare(self):
        runner = self.runner
        runner.reset_to_home(self.cfg_dict)
        runner.start_keyboard_listener()
        logger.success("Prepare finished.")
        return runner

    def calibrate(self,camera_name: str = "front",arm_name: str = "single", cali_nums: int = 30, 
                  auto_read: bool = True, save_path: str = None, config_path: str = None) -> bool:
        """Calibrate camera extrinsics and save intrinsics/extrinsics to a JSON file.

        Args:
            camera_name.
            arm_name.
            cali_nums: The name of the camera and arm, and the number of calibration images to capture.
            auto_read: If True, read the camera intrinsics from a JSON file.
            save_path: If None save_path = os.path.expanduser(f'~/Dev/sysEAI/xRocs/xrocs/entity/calibration/arm_{arm_name}_{camera_name}_extrinsics.json').
            config_path: If None config_path = os.path.expanduser("~/Documents/configuration.toml").

        Returns:
            np.ndarray: The extrinsic transformation matrix.
        """
        hand_camera = True
        if save_path is None:
            save_path = os.path.expanduser(f'~/Dev/sysEAI/xRocs/xrocs/entity/calibration/arm_{arm_name}_{camera_name}_extrinsics.json')
        set_calib_info_path(save_path)
        if config_path is None:
            config_path = os.path.expanduser("~/Documents/configuration.toml")
        config = ConfigLoader(config_path).get_config()
        camera_serial = str(config['camera'][camera_name]['serial'])

        # get intrinsics
        if auto_read == True:
            try:
                # read intrinsics
                calib_dir = os.path.expanduser("~/Dev/sysEAI/xRocs/xrocs/entity/calibration/")
                # find target json file
                for filename in os.listdir(calib_dir):
                    if filename.endswith(".json") and camera_serial in filename:
                        file_path = os.path.join(calib_dir, filename)
                        # read JSON file
                        with open(file_path, "r") as f:
                            intrinsics_data = json.load(f)
                        break
                print(f"\n=== Target file: {filename} ===")
                # TODO: add camera parameters to config
                intrinsics_dict = {}
                intrinsics_dict[camera_name] = intrinsics_data[camera_serial]
            except:
                logger.error(f"Cannot find the camera serial number {camera_serial}. Please check the camera camera_name and arm_name in {config_path}.")
                return False
        else:
            intrinsics_dict = {
            # wrist
            camera_name: {"cameraMatrix": [[607.2721557617188, 0, 325.0840759277344], [0, 607.2348022460938, 246.19912719726562], [0, 0, 1]], 
                          "distCoeffs": [0.0, 0.0, 0.0, 0.0, 0.0]}
            
            }
        
        if hand_camera:
            calibrator = HandCameraCalibrator(intrinsics_dict)
        else:
            calibrator = ThirdPersonCameraCalibrator(intrinsics_dict)

        print("enter left button to go")
        while self.runner.btn_preparing:
            env_obs = self.robot_station.get_obs()
            env_images = env_obs['images'][camera_name]
            env_depth = env_obs['depths'][camera_name]
            cam_obs, _ = self.decoder_image(env_images, env_depth)
            calibrator.augment_image(camera_name, cam_obs, visualize=True, visual_type=['charuco'])
            action = self.robot_station.decompose_action(self.tele_agent.act())
            self.robot_station.step(action)
            if self.runner.btn_exit: return

        print(f"Began calibration for {camera_name}")
        m_count = 0
        while True:
            # Check For Termination #
            if self.runner.btn_exit: 
                self.runner.reset_to_home(self.cfg_dict)
                return
            action = self.robot_station.decompose_action(self.tele_agent.act())
            self.robot_station.step(action)

            env_obs = self.robot_station.get_obs()
            env_images = env_obs['images'][camera_name]
            env_depth = env_obs['depths'][camera_name]
            cam_obs, _ = self.decoder_image(env_images, env_depth)
            calibrator.augment_image(camera_name, cam_obs, visualize=True, visual_type=['charuco'])
            # Add Sample + Augment Images #
            if self.runner.btn_stop:
                img = deepcopy(cam_obs)
                pose = env_obs["arm_pose"][arm_name]
                calibrator.add_sample(camera_name, img, pose)
                m_count += 1
                print(f"There are {cali_nums-m_count+1} pictures left.")
                self.runner.btn_stop = False

            if m_count > cali_nums:
                break
            self.robot_station._rate.sleep()

        # get extrinsics
        transformation = calibrator.calibrate(camera_name)
        success = calibrator.is_calibration_accurate(camera_name)
        if not success:
            logger.error("Calibration Failed!")
        else:
            logger.success("Calibration Success!")

        # SAVE INTO A JSON
        update_calibration_info(camera_name, intrinsics_dict, transformation, camera_serial)
        self.runner.reset_to_home(self.cfg_dict)
        
        return transformation


if __name__ == '__main__':
    data = StationCali()
    data.prepare()
    data.calibrate(camera_name = "front",arm_name = "single", cali_nums = 30, auto_read = True)
