import time
import numpy as np

from xrocs.utils.logger.logger_loader import logger

from xrocs.common.data_type import Joints, Coordinate, Pose, Rotation



def creat_circle_points(center, r, num):
    # Generate circular path points, based on (center, r, num)
    center = np.array(center)
    waypoints = [center] * num

    theta = np.linspace(0, 2*np.pi, num, endpoint=False)  # Uniform angular distribution

    waypoints = np.array([
        [center[0] + r * np.cos(theta[i]), center[1] + r * np.sin(theta[i]), center[2], center[3], center[4], center[5]]
        for i in range(num)
    ])

    # logger.success("Creat finished")
    return np.array(waypoints)

def check_ur(config: dict, success_num: int = 0, failed_num: int = 0):  
    try:
        from xrocs.entity.robot.UR.ur import URRobot
        # Movement parameters
        points_num = 5
        circle_r = 0.05
        robot_list = []
        # Check each robot in the configuration
        for robot_name in config['robot']['arm']['ip'].keys():
            try:
                robot_ip = config['robot']['arm']['ip'][robot_name]
                ur_robot = URRobot(robot_ip)
                robot_list.append([robot_name, ur_robot])
                robot_home = config['robot']['arm']['home'][robot_name]

                ur_robot.connect()

                # 1. Move to home position
                ur_robot.reach_target_joint(Joints(robot_home))
                logger.info(f"{robot_name} Go home position")
                time.sleep(1)
                # 2. Perform circular test movement
                cur_pose = ur_robot.get_tool_cartesian_pose().get_xyz_m_rpy_radian_ndarray()
                logger.info(f"{robot_name} Ready to move")
                # Generate circular path points
                target_points = creat_circle_points(cur_pose, circle_r, points_num)
                # Move to first point
                ur_robot.reach_tool_cartesian_pose(Pose(Coordinate(target_points[0][:3]), Rotation(target_points[0][3:], rotation_form_type=Rotation.FormType.RPY)))
                time.sleep(1)
                # Move through each point
                for i in range(1, points_num):
                    ur_robot.reach_tool_cartesian_pose(Pose(Coordinate(target_points[i][:3]), Rotation(target_points[i][3:], rotation_form_type=Rotation.FormType.RPY)))
                logger.info(f"{robot_name} Go home position")
                # 3. Return to home position
                ur_robot.reach_target_joint(Joints(robot_home))

                success_num += 1
                logger.success(f"check UR [{robot_name.upper()}] success")
            except Exception as e:
                failed_num += 1
                logger.error(f"check UR [{robot_name.upper()}] failed: {str(e)}")

    except Exception as e:
        failed_num += 1
        logger.error(f"check UR failed: {str(e)}")

    return success_num, failed_num

if __name__ == '__main__':
    from xrocs.core.config_loader import ConfigLoader
    config_path = "~/Documents/configuration.toml"
    config = ConfigLoader(config_path).get_config()
    check_ur(config)