import time

from xrocs.core.config_loader import ConfigLoader
from xrocs.core.station_loader import StationLoader
from xtele.core.integrate_module import TeleCore


config_path = "/home/<USER>/Documents/configuration.toml"
cfg_loader = ConfigLoader(config_path)
cfg_dict = cfg_loader.get_config()
station_loader = StationLoader(cfg_dict)
robot_station = station_loader.generate_station_handle()
tele_agent = TeleCore()

robot_station.connect()

for _ in range(10):
    print(robot_station.get_obs())
    time.sleep(1)