import numpy as np
import cv2

from xrocs.utils.logger.logger_loader import logger


from xrocs.entity.camera.orbbec_camera import OrbbecClientCamera


def decoder_image(camera_rgb_images, camera_depth_images, bgr2rgb=False):
    # Decode streamed data
    if type(camera_rgb_images[0]) is np.uint8:
        rgb = cv2.imdecode(camera_rgb_images, cv2.IMREAD_COLOR)
        if camera_depth_images is not None:
            depth_array = np.frombuffer(camera_depth_images, dtype=np.uint8)
            depth = cv2.imdecode(depth_array, cv2.IMREAD_UNCHANGED)
        else:
            depth = np.asarray([])
        return rgb, depth
    else:
        rgb_images = []
        depth_images = []
        # print(f"camera_rgb_images: {camera_rgb_images}")
        for idx, camera_rgb_image in enumerate(camera_rgb_images):
            # print(f"camera_rgb_image: {camera_rgb_image}")
            rgb = cv2.imdecode(camera_rgb_image, cv2.IMREAD_COLOR)
            if camera_depth_images is not None:
                depth_array = np.frombuffer(camera_depth_images[idx], dtype=np.uint8)
                depth = cv2.imdecode(depth_array, cv2.IMREAD_UNCHANGED)
            else:
                depth = np.asarray([])
            if bgr2rgb and rgb is not None:
                rgb = cv2.cvtColor(rgb, cv2.COLOR_BGR2RGB)
            rgb_images.append(rgb)
            depth_images.append(depth)
        rgb_images = np.asarray(rgb_images)
        depth_images = np.asarray(depth_images)
        return rgb_images, depth_images

def check_orbbec(config, success_num=0, failed_num=0):
    port_list = list(config['camera']['dict'].values())
    cameras = []
    
    try:
        # Initialize all cameras
        for port in port_list:
            camera = OrbbecClientCamera(port=port)
            cameras.append(camera)
        
        while True:
            # Store images from all cameras
            frames = []
            # Read data from each camera
            for camera in cameras:
                images, depth = camera.read()
                img, _ = decoder_image(images, depth)
                bgr_img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                bgr_img = cv2.resize(bgr_img, (320, 240))  # Uniformly scale to smaller size
                frames.append(bgr_img)

            # Dynamically calculate grid layout
            num_cameras = len(frames)

            # Calculate optimal number of rows and columns (close to square layout)
            cols = int(np.ceil(np.sqrt(num_cameras)))
            rows = int(np.ceil(num_cameras / cols))
            
            # Fill blank images (ensure grid completeness)
            blank = np.zeros_like(frames[0]) if frames else np.zeros((240, 320, 3), dtype=np.uint8)
            frames += [blank] * (rows * cols - num_cameras)
            
            # Construct grid
            grid = []
            for i in range(rows):
                row = np.hstack(frames[i*cols : (i+1)*cols])
                grid.append(row)
            combined = np.vstack(grid)
            
            # Display
            cv2.imshow("Multi-Camera View", combined)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

        success_num += 1
        logger.success(f"check Orbbec camera success")

    except Exception as e:
        failed_num += 1
        logger.error(f"check Orbbec camera failed: {str(e)}")

    finally:
        cv2.destroyAllWindows()
    
    return success_num, failed_num

if __name__ == '__main__':
    from xrocs.core.config_loader import ConfigLoader
    config_path = "~/Documents/configuration.toml"
    config = ConfigLoader(config_path).get_config()
    check_orbbec(config)