from xrocs.entity.robot.Franka.franka import <PERSON>aRobot
import numpy as np
from scipy.spatial.transform import Rotation as R
from xrocs.common.data_type import Joints, Pose, Coordinate, Rotation

from xrocs.utils.logger.logger_loader import logger



def check_franka(config: dict, success_num: int = 0, failed_num: int = 0):
    try:
        # control_ip = '127.0.0.1'
        robot_list = []
        # Iterate through the robots defined in the IP list and check them one by one
        for robot_name in config['robot']['arm']['ip'].keys():
            try:
                robot_ip = config['robot']['arm']['ip'][robot_name]
                franka_robot = <PERSON>aRobot(robot_ip=robot_ip, arm_type=robot_name)
                robot_list.append([robot_name, franka_robot])
            

                franka_robot.connect()
                robot_home = Joints(config['robot']['arm']['home'][robot_name], num_of_dofs=7)
                # Go Home
                franka_robot.reach_target_joint(robot_home)
                # Generate coordinates of the four corners of a square centered at home
                cur_franka_pose = franka_robot.get_tool_cartesian_pose()
                cur_franka_pose = cur_franka_pose.get_xyz_m_rpy_radian_ndarray()
                center_x, center_y, center_z = cur_franka_pose[:3]
                r = 0.05

                target_positions_list = [
                    [center_x+r, center_y+r, center_z],
                    [center_x+r, center_y-r, center_z],
                    [center_x-r, center_y+r, center_z],
                    [center_x-r, center_y-r, center_z]
                ]
                # Move to each target position sequentially
                for target_position in target_positions_list:
                    franka_robot.reach_tool_cartesian_pose(Pose(Coordinate(target_position), Rotation(cur_franka_pose[3:])))
                    # time.sleep(0.5)
                # Go Home
                franka_robot.reach_target_joint(robot_home)

                success_num += 1
                logger.success(f"check Franka [{robot_name.upper()}] success")
            except Exception as e:
                failed_num += 1
                logger.error(f"check Franka [{robot_name.upper()}] failed: {str(e)}")


    except Exception as e:
        failed_num += 1
        logger.error(f"check Franka failed: {str(e)}")
    
    return success_num, failed_num


if __name__ == '__main__':
    from xrocs.core.config_loader import ConfigLoader
    config_path = "~/Documents/configuration.toml"
    config = ConfigLoader(config_path).get_config()
    check_franka(config)