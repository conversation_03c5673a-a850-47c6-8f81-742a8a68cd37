import numpy as np
import cv2
import math

from xrocs.utils.logger.logger_loader import logger

try:
    from .check_franka import check_franka
    from .check_ur import check_ur
    from .check_realsense import check_realsense
    from .check_orbbec import check_orbbec
except:
    from xrocs.apps.status_checking.check_franka import check_franka
    from xrocs.apps.status_checking.check_ur import check_ur
    from xrocs.apps.status_checking.check_realsense import check_realsense
    from xrocs.apps.status_checking.check_orbbec import check_orbbec



def check_hardware(config: dict):
    global success_num, failed_num
    success_num, failed_num = 0, 0
    select_list = []

    station_type = config['basic']['station_type'] # Read station_type from the configuration file as the basis for determining the station type

    if "franka" in station_type.lower():
        select_list.append("Franka")
        success_num, failed_num = check_franka(config, success_num, failed_num)
    elif "ur" in station_type.lower():
        select_list.append("UR")
        success_num, failed_num = check_ur(config, success_num, failed_num)

    if "rs" in station_type.lower() or "realsense" in station_type.lower():
        select_list.append("RealSense")
        success_num, failed_num = check_realsense(config, success_num, failed_num)
    else:
        select_list.append("Orbbec")
        success_num, failed_num = check_orbbec(config, success_num, failed_num)
    

    logger.info(f"selected:{select_list}")
    if failed_num == 0:
        logger.success(f"Check finished, success{success_num}/{success_num+failed_num}")
    else:
        logger.error(f"Check finished, success {success_num}/{success_num+failed_num}")
    

if __name__ == '__main__':
    from xrocs.core.config_loader import ConfigLoader
    config_path = "~/Documents/configuration.toml"
    config = ConfigLoader(config_path).get_config()
    check_hardware(config)

    
