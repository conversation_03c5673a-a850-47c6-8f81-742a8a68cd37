import math
import pyrealsense2 as rs
import cv2
import numpy as np
from typing import List

from xrocs.utils.logger.logger_loader import logger


def check_realsense(config: dict, success_num: int = 0, failed_num: int = 0) -> tuple[int, int]:
    try:
        ctx = rs.context()
        # Get camera serial numbers
        devices = ctx.query_devices()
        serials = [dev.get_info(rs.camera_info.serial_number) for dev in devices]

        # Store pipeline objects for each camera
        pipelines = []
        # Configure and start pipeline for each camera
        for serial in serials:
            config = rs.config()
            config.enable_device(serial)  # Enable the device with the specified serial number
            config.enable_stream(rs.stream.color, 1280, 720, rs.format.bgr8, 30)  # Configure color stream: 1280x720 resolution, BGR8 format, 30 FPS
            pipeline = rs.pipeline(ctx)
            pipeline.start(config)
            pipelines.append(pipeline)

        # Check if cameras are connected
        if not serials:
            raise ValueError("No RealSense devices detected!")
        else:
            print(f"find camera num {len(serials)}:\n{serials}")

        # Adaptively calculate window size
        num_cameras = len(serials)
        cols = int(np.ceil(np.sqrt(num_cameras)))
        rows = int(np.ceil(num_cameras / cols))
        window_w = 300
        window_h = 200
        cv2.namedWindow('Camera Grid', cv2.WINDOW_NORMAL)
        
        # Create and refresh the window
        while True:
            images = []
            # Get frames from each camera
            for pipeline, serial in zip(pipelines, serials):
                frames = pipeline.wait_for_frames()  # Wait for frame data
                color_frame = frames.get_color_frame()
                
                if color_frame:
                    # Convert frame data to numpy array and resize
                    img = np.asanyarray(color_frame.get_data())
                    img = cv2.resize(img, (window_w, window_h))
                    # Add serial number to the image
                    cv2.putText(img, f"Serial: {serial}", (10, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                else:
                    # Create a blank image if no frame is received
                    img = np.zeros((window_h, window_w, 3), dtype=np.uint8)
                    
                images.append(img)

            # Fill remaining grid positions with blank images if the number of cameras is insufficient
            while len(images) < rows * cols:
                images.append(np.zeros((window_h, window_w, 3), dtype=np.uint8))

            # Create grid layout
            grid = []
            for r in range(rows):
                row_start = r * cols
                # Get images for the current row
                row_images = images[row_start : row_start + cols]
                # Horizontally concatenate images
                grid.append(np.hstack(row_images))
            
            # Vertically concatenate all rows
            full_image = np.vstack(grid)
            # Display the grid image
            cv2.imshow('Camera Grid', full_image)
            if cv2.waitKey(1) in [ord('q'), 27]:
                break
        success_num += 1
        logger.success(f"check RealSense camera success")

    except Exception as e:
        failed_num += 1
        logger.error(f"check RealSense camera failed: {str(e)}")

    finally:
        for p in pipelines:
            p.stop()
        cv2.destroyAllWindows()

    return success_num, failed_num
    

if __name__ == '__main__':
    from xrocs.core.config_loader import ConfigLoader
    config_path = "~/Documents/configuration.toml"
    config = ConfigLoader(config_path).get_config()
    check_realsense(config)