import time

from xrocs.core.app_base import Runner<PERSON><PERSON>
from xrocs.core.config_loader import ConfigLoader
from xrocs.core.station_loader import StationLoader
from xtele.core.integrate_module import TeleCore


config_path = "/home/<USER>/Documents/configuration.toml"
cfg_loader = ConfigLoader(config_path)
cfg_dict = cfg_loader.get_config()
station_loader = StationLoader(cfg_dict)
robot_station = station_loader.generate_station_handle()
tele_agent = TeleCore()

runner = RunnerBase(tele_agent, robot_station)
robot_station.connect()

runner.reset_to_home(cfg_dict)
runner.check_pre_sync_status()
runner.sync_agent_to_env()
runner.start_keyboard_listener()

while runner.btn_preparing:
    robot_targets = robot_station.decompose_action(tele_agent.act())
    obs = robot_station.step(robot_targets)
    if runner.btn_exit:
        runner.speaker.speak("关闭程序。", sync=False)
        robot_station.close()
        break
