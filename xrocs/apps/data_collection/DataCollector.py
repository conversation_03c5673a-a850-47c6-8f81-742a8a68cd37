import datetime
from pathlib import Path
from tqdm import tqdm
import cv2
import numpy as np
from xrocs.core.config_loader import ConfigLoader
from xrocs.utils.logger.logger_loader import logger
from xrocs.core.app_base import RunnerBase
from xrocs.utils.DataSaver import save_frame, save_trajectory
from xtele.core.integrate_module import TeleCore
from xrocs.core.station_loader import StationLoader


class DataCollector:
    def __init__(self, config_path : str = None):
        if config_path is None:
            config_path = "/home/<USER>/Documents/configuration.toml"
        cfg_loader = ConfigLoader(config_path)
        self.cfg_dict = cfg_loader.get_config()
        station_loader = StationLoader(self.cfg_dict)
        self.robot_station = station_loader.generate_station_handle()
        self.tele_agent = TeleCore()
        self.runner = RunnerBase(self.tele_agent, self.robot_station)
        self.robot_station.connect()
        self.episode_length = 20000
        self.max_episode = 2000

    def prepare(self):
        """
        Prepare for station initialization
        """
        runner = self.runner
        runner.reset_to_home(self.cfg_dict)
        runner.check_pre_sync_status()
        runner.sync_agent_to_env()
        runner.start_keyboard_listener()
        logger.success("Prepare finished.")
        return
    
    def collect(self, save_path: Path, task):
        """
        Collect data for a single task
        
        Args:
            save_path (Path): path to save the data
            task (dict): task to collect data for (e.g. {"name": "task_name", "description": "中文描述，方便采集播报"})
        
        Returns:
            None
        """
        task_name = task['name']
        task_desc = task['description']
        logger.info(f"\nProcessing {task_name}")
        logger.info("按下开始键开始采集")
        while self.runner.btn_preparing:
            robot_targets = self.robot_station.decompose_action(self.tele_agent.act())
            obs = self.robot_station.step(robot_targets)
            if self.cfg_dict["collect"]["is_display_image"]:
                self.display_image(obs["images"])
            if self.runner.btn_exit:
                self.runner.speaker.speak("关闭程序。", sync=False)
                self.robot_station.close()
                return

        self.runner.speaker.speak(f"开始采集，当前任务是： {task_desc}")
        self.runner.btn_stop = False
        self.runner.btn_preparing = True

        obs = self.robot_station.get_obs()
        if self.cfg_dict["collect"]["is_display_image"]:
            self.display_image(obs["images"])
        trajectory_obs = []
        trajectory_actions = []
        for _ in tqdm(range(self.episode_length),
                            position=0, leave=True):
            action = self.robot_station.decompose_action(self.tele_agent.act())
            dt = datetime.datetime.now()
            if self.cfg_dict['collect']['save_type'] == 'pickle':
                save_frame(save_path, dt, obs, action)
            elif self.cfg_dict['collect']['save_type'] == 'hdf5':
                trajectory_obs.append(obs)
                trajectory_actions.append(action)
            else:
                raise ValueError("Invalid save type in config file.")
            obs = self.robot_station.step(action)
            if self.cfg_dict["collect"]["is_display_image"]:
                self.display_image(obs["images"])
            if self.runner.btn_stop:
                break
            if self.runner.btn_exit:
                self.runner.speaker.speak("关闭程序。", sync=False, block=False)
                self.robot_station.close()
                if self.cfg_dict['collect']['save_type'] == 'hdf5':
                    save_trajectory(save_path, trajectory_obs, trajectory_actions)
                    self.runner.speaker.speak("保存数据完成", sync=False)
                return

        self.runner.speaker.speak(f"采集结束，请停止动作", sync=False, block=True)
        if self.cfg_dict['collect']['save_type'] == 'hdf5':
            save_trajectory(save_path, trajectory_obs, trajectory_actions)
            self.runner.speaker.speak("保存数据完成", sync=False)
            del trajectory_obs, trajectory_actions
        self.runner.btn_preparing = True
        self.runner.btn_stop = False
        return
    
    def display_image(self, images):
        rows = 2
        cols = 3
        window_w = 600
        window_h = 400
        display_img = []
        for name, image in images.items():
            image = cv2.imdecode(image, cv2.IMREAD_COLOR)
            if 1:
                img = np.asanyarray(image)
                img = cv2.resize(img, (window_w, window_h))
            else:
                img = np.zeros((window_h, window_w, 3), dtype=np.uint8)
            display_img.append(img)

        while len(display_img) < rows * cols:
            display_img.append(np.zeros((window_h, window_w, 3), dtype=np.uint8))

        grid = []
        for r in range(rows):
            row_start = r * cols
            row_images = display_img[row_start : row_start + cols]
            grid.append(np.hstack(row_images))
        
        full_image = np.vstack(grid)
        cv2.imshow('Camera Grid', full_image)
        cv2.waitKey(1) in [ord('q'), 27]


    def start_collect(self, data_dir: str, task_sequence: list):
        for i in range(self.max_episode):
            for tid, task in enumerate(task_sequence):
                if self.runner.btn_exit:
                    self.runner.speaker.speak("关闭程序。", sync=True)
                    return
                dt_time = datetime.datetime.now()
                save_path = (
                        Path(f'{data_dir}/{task["name"]}').expanduser()
                        / dt_time.strftime("%m%d_%H%M%S"))
                save_path.mkdir(parents=True, exist_ok=True)
                self.collect(save_path, task)
                logger.info(f"Saving to {save_path}", f'with number of {i} samples')
