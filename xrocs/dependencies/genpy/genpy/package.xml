<?xml version="1.0"?>
<?xml-model
  href="http://download.ros.org/schema/package_format3.xsd"
  schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>genpy</name>
  <version>0.6.16</version>
  <description>Python ROS message and service generators.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>

  <url type="website">http://wiki.ros.org/genpy</url>
  <url type="bugtracker">https://github.com/ros/genpy/issues</url>
  <url type="repository">https://github.com/ros/genpy</url>

  <author><PERSON></author>
  <author><PERSON></author>
  <author><PERSON><PERSON></author>
  <author><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <depend>genmsg</depend>

  <buildtool_depend version_gte="0.5.78">catkin</buildtool_depend>
  <buildtool_depend condition="$ROS_PYTHON_VERSION == 2">python-setuptools</buildtool_depend>
  <buildtool_depend condition="$ROS_PYTHON_VERSION == 3">python3-setuptools</buildtool_depend>

  <exec_depend condition="$ROS_PYTHON_VERSION == 2">python-yaml</exec_depend>
  <exec_depend condition="$ROS_PYTHON_VERSION == 3">python3-yaml</exec_depend>

  <test_depend condition="$ROS_PYTHON_VERSION == 2">python-numpy</test_depend>
  <test_depend condition="$ROS_PYTHON_VERSION == 3">python3-numpy</test_depend>

  <export>
    <message_generator>py</message_generator>
    <rosdoc config="rosdoc.yaml"/>
    <architecture_independent/>
  </export>
</package>
