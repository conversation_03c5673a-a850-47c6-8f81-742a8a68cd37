try:
  length = len(self.array)
  buff.write(_struct_I.pack(length))
  for val1 in self.array:
    _x = val1.data
    buff.write(_get_struct_i().pack(_x))
except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))
