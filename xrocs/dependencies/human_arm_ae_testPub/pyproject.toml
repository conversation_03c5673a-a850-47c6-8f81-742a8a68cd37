[project]
name = "human_arm_ae_testPub"
version = "0.1"
description = "human_arm_ae_testPub"
authors = [
    { name = "<PERSON>", email = "<PERSON><PERSON>@x-humanoid.com" }
]
#readme = "README.md"
requires-python = ">= 3.8"
#license = { file = "LICENSE" }
keywords = [
]

classifiers=[
    'Development Status :: 4 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.9',
]

dependencies = [
]

[project.urls]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["human_arm_ae_testPub"]
