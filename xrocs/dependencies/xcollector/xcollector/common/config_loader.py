import argparse
import os
from typing import Any

import tomli as tomllib

from xcollector.common.logger_loader import logger

__all__ = ['config', 'config_loader']


class _ConfigLoader:

    def __init__(self) -> None:
        # get current file path
        config_path_name = os.path.join(os.path.dirname(__file__), 'config', 'configuration.toml')
        with open(config_path_name, 'rb') as f:
            # Need to explicitly cast dict to TypedDict, without check.
            self.config = tomllib.load(f)
        logger.info(f'Default configure file {config_path_name} loaded.')

        self.parser = argparse.ArgumentParser(add_help=False)
        self.parser.add_argument('-h', '--help', action='store_true', help='show this help message and exit')
        self.parser.add_argument('--robot_name', type=str, help='the robot name')
        args = self.parser.parse_known_args()[0]

        if args.help:
            self.parser.print_help()
            exit()

        robot_name = args.robot_name

        if not robot_name:
            logger.warning('This node starts without startup parameters and runs with default configuration.')
            robot_name = self.config['global_setting']['robot_name']

        if not robot_name:
            logger.warning('No robot_name is specified in the configuration file.')
        else:
            logger.info(f'This node starts with the parameters: --robot_name {robot_name} ')
            local_config_file_name = f'configuration_{robot_name}.toml'

            local_config_path_name = os.path.join(os.path.dirname(__file__), 'config', local_config_file_name)
            if not os.path.exists(local_config_path_name):
                logger.warning(f'There is no local configuration file {local_config_file_name} '
                               f'and runs with default configuration.')
            else:
                logger.info(f'Replace default configuration with {local_config_file_name}')

                # Use Any because the input parameters for recursive calls are uncertain.
                def update_config_dict(  # type:ignore[misc]
                        default_dict: Any, local_dict: Any, parent_key: str) -> None:
                    if parent_key:
                        parent_key += '.'
                    for key, value in local_dict.items():
                        if isinstance(value, dict):
                            if key not in default_dict:
                                default_dict[key] = value
                            update_config_dict(default_dict[key], value, parent_key + key)
                        else:
                            logger.info(
                                f'Replace configuration: [{parent_key+key}] = {default_dict[key]} --> {value}.')
                            default_dict[key] = value

                with open(local_config_path_name, 'rb') as local_f:
                    local_config = tomllib.load(local_f)
                    update_config_dict(self.config, local_config, '')

config_loader = _ConfigLoader()
config = config_loader.config
