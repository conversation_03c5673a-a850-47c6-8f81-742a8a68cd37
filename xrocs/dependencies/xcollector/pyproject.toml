[project]
name = "xcollector"
version = "0.1"
description = "puppet teleoperation system fir x-humanoid"
authors = [
    { name = "<PERSON>", email = "<PERSON><PERSON>@x-humanoid.com" }
]
#readme = "README.md"
requires-python = ">= 3.8"
#license = { file = "LICENSE" }
keywords = [
    'teleoperation',
    'robotic system',
]

classifiers=[
    'Development Status :: 4 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'License :: OSI Approved :: MIT License',
    'Programming Language :: Python :: 3.8',
]

dependencies = [
    "loguru",
    "numpy",
    "pynput",
    "python_vlc",
    "Requests",
    "tomli",
    "tqdm",
    "zerorpc",
    "opencv-python",
    "matplotlib",
    "scipy",
    "tyro",
    "pyzmq"
]

[project.urls]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["xcollector"]

[tool.setuptools.packages.find]
where = ["xcollector"]
