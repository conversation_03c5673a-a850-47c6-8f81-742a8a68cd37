<?xml version="1.0"?>
<?xml-model
  href="http://download.ros.org/schema/package_format3.xsd"
  schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>genmsg</name>
  <version>0.5.16</version>
  <description>
    Standalone Python library for generating ROS message and service data structures for various languages.
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>BSD</license>

  <url type="website">http://wiki.ros.org/genmsg</url>
  <url type="bugtracker">https://github.com/ros/genmsg/issues</url>
  <url type="repository">https://github.com/ros/genmsg</url>

  <author><PERSON></author>
  <author><PERSON><PERSON></author>
  <author><PERSON></author>
  <author><PERSON></author>

  <buildtool_depend version_gte="0.5.74">catkin</buildtool_depend>
  <buildtool_depend condition="$ROS_PYTHON_VERSION == 2">python-setuptools</buildtool_depend>
  <buildtool_depend condition="$ROS_PYTHON_VERSION == 3">python3-setuptools</buildtool_depend>
  <exec_depend>catkin</exec_depend>
  <exec_depend condition="$ROS_PYTHON_VERSION == 2">python-empy</exec_depend>
  <exec_depend condition="$ROS_PYTHON_VERSION == 3">python3-empy</exec_depend>

  <export>
    <rosdoc config="rosdoc.yaml"/>
    <architecture_independent/>
  </export>
</package>
