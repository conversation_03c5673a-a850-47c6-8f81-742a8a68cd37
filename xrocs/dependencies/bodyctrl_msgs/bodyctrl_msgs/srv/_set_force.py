# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/set_forceRequest.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class set_forceRequest(genpy.Message):
  _md5sum = "b85daa8a508267c29c0e9abef4453eb9"
  _type = "bodyctrl_msgs/set_forceRequest"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """float32 force0Ratio
float32 force1Ratio
float32 force2Ratio
float32 force3Ratio
float32 force4Ratio
float32 force5Ratio
"""
  __slots__ = ['force0Ratio','force1Ratio','force2Ratio','force3Ratio','force4Ratio','force5Ratio']
  _slot_types = ['float32','float32','float32','float32','float32','float32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       force0Ratio,force1Ratio,force2Ratio,force3Ratio,force4Ratio,force5Ratio

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(set_forceRequest, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.force0Ratio is None:
        self.force0Ratio = 0.
      if self.force1Ratio is None:
        self.force1Ratio = 0.
      if self.force2Ratio is None:
        self.force2Ratio = 0.
      if self.force3Ratio is None:
        self.force3Ratio = 0.
      if self.force4Ratio is None:
        self.force4Ratio = 0.
      if self.force5Ratio is None:
        self.force5Ratio = 0.
    else:
      self.force0Ratio = 0.
      self.force1Ratio = 0.
      self.force2Ratio = 0.
      self.force3Ratio = 0.
      self.force4Ratio = 0.
      self.force5Ratio = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_6f().pack(_x.force0Ratio, _x.force1Ratio, _x.force2Ratio, _x.force3Ratio, _x.force4Ratio, _x.force5Ratio))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 24
      (_x.force0Ratio, _x.force1Ratio, _x.force2Ratio, _x.force3Ratio, _x.force4Ratio, _x.force5Ratio,) = _get_struct_6f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_6f().pack(_x.force0Ratio, _x.force1Ratio, _x.force2Ratio, _x.force3Ratio, _x.force4Ratio, _x.force5Ratio))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 24
      (_x.force0Ratio, _x.force1Ratio, _x.force2Ratio, _x.force3Ratio, _x.force4Ratio, _x.force5Ratio,) = _get_struct_6f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_6f = None
def _get_struct_6f():
    global _struct_6f
    if _struct_6f is None:
        _struct_6f = struct.Struct("<6f")
    return _struct_6f
# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/set_forceResponse.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class set_forceResponse(genpy.Message):
  _md5sum = "6133e5998441599a96899c74dc63d85d"
  _type = "bodyctrl_msgs/set_forceResponse"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """bool force_accepted

"""
  __slots__ = ['force_accepted']
  _slot_types = ['bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       force_accepted

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(set_forceResponse, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.force_accepted is None:
        self.force_accepted = False
    else:
      self.force_accepted = False

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self.force_accepted
      buff.write(_get_struct_B().pack(_x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 1
      (self.force_accepted,) = _get_struct_B().unpack(str[start:end])
      self.force_accepted = bool(self.force_accepted)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self.force_accepted
      buff.write(_get_struct_B().pack(_x))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      start = end
      end += 1
      (self.force_accepted,) = _get_struct_B().unpack(str[start:end])
      self.force_accepted = bool(self.force_accepted)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_B = None
def _get_struct_B():
    global _struct_B
    if _struct_B is None:
        _struct_B = struct.Struct("<B")
    return _struct_B
class set_force(object):
  _type          = 'bodyctrl_msgs/set_force'
  _md5sum = '6b0fdeb4ed7ee4c97030abfd78488ebb'
  _request_class  = set_forceRequest
  _response_class = set_forceResponse
