# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/MotorName.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class MotorName(genpy.Message):
  _md5sum = "b27f93e5cd91bc5913c47d18edc04829"
  _type = "bodyctrl_msgs/MotorName"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# head 00 + id
uint16 MOTOR_HEAD_1 = 1
uint16 MOTOR_HEAD_2 = 2
uint16 MOTOR_HEAD_3 = 3

# arm-left 10 + id
uint16 MOTOR_ARM_LEFT_1 = 11
uint16 MOTOR_ARM_LEFT_2 = 12
uint16 MOTOR_ARM_LEFT_3 = 13
uint16 MOTOR_ARM_LEFT_4 = 14
uint16 MOTOR_ARM_LEFT_5 = 15
uint16 MOTOR_ARM_LEFT_6 = 16
uint16 MOTOR_ARM_LEFT_7 = 17

# arm-right 20 + id
uint16 MOTOR_ARM_RIGHT_1 = 21
uint16 MOTOR_ARM_RIGHT_2 = 22
uint16 MOTOR_ARM_RIGHT_3 = 23
uint16 MOTOR_ARM_RIGHT_4 = 24
uint16 MOTOR_ARM_RIGHT_5 = 25
uint16 MOTOR_ARM_RIGHT_6 = 26
uint16 MOTOR_ARM_RIGHT_7 = 27

# body (including waist) 30 + id
# uint16 MOTOR_WAIST = 31

# 40 rfu

# leg-left 50 + id
uint16 MOTOR_LEG_LEFT_1 = 51
uint16 MOTOR_LEG_LEFT_2 = 52
uint16 MOTOR_LEG_LEFT_3 = 53
uint16 MOTOR_LEG_LEFT_4 = 54
uint16 MOTOR_LEG_LEFT_5 = 55
uint16 MOTOR_LEG_LEFT_6 = 56

# leg-right 60 + id
uint16 MOTOR_LEG_RIGHT_1 = 61
uint16 MOTOR_LEG_RIGHT_2 = 62
uint16 MOTOR_LEG_RIGHT_3 = 63
uint16 MOTOR_LEG_RIGHT_4 = 64
uint16 MOTOR_LEG_RIGHT_5 = 65
uint16 MOTOR_LEG_RIGHT_6 = 66




"""
  # Pseudo-constants
  MOTOR_HEAD_1 = 1
  MOTOR_HEAD_2 = 2
  MOTOR_HEAD_3 = 3
  MOTOR_ARM_LEFT_1 = 11
  MOTOR_ARM_LEFT_2 = 12
  MOTOR_ARM_LEFT_3 = 13
  MOTOR_ARM_LEFT_4 = 14
  MOTOR_ARM_LEFT_5 = 15
  MOTOR_ARM_LEFT_6 = 16
  MOTOR_ARM_LEFT_7 = 17
  MOTOR_ARM_RIGHT_1 = 21
  MOTOR_ARM_RIGHT_2 = 22
  MOTOR_ARM_RIGHT_3 = 23
  MOTOR_ARM_RIGHT_4 = 24
  MOTOR_ARM_RIGHT_5 = 25
  MOTOR_ARM_RIGHT_6 = 26
  MOTOR_ARM_RIGHT_7 = 27
  MOTOR_LEG_LEFT_1 = 51
  MOTOR_LEG_LEFT_2 = 52
  MOTOR_LEG_LEFT_3 = 53
  MOTOR_LEG_LEFT_4 = 54
  MOTOR_LEG_LEFT_5 = 55
  MOTOR_LEG_LEFT_6 = 56
  MOTOR_LEG_RIGHT_1 = 61
  MOTOR_LEG_RIGHT_2 = 62
  MOTOR_LEG_RIGHT_3 = 63
  MOTOR_LEG_RIGHT_4 = 64
  MOTOR_LEG_RIGHT_5 = 65
  MOTOR_LEG_RIGHT_6 = 66

  __slots__ = []
  _slot_types = []

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(MotorName, self).__init__(*args, **kwds)

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      pass
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      pass
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
