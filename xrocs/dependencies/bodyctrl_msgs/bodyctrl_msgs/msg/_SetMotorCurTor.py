# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/SetMotorCurTor.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class SetMotorCurTor(genpy.Message):
  _md5sum = "9129d144b75ff8a02015501091dfe78e"
  _type = "bodyctrl_msgs/SetMotorCurTor"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """uint16 name # MotorName
int16 cur_tor
uint8 ctrl_status
"""
  __slots__ = ['name','cur_tor','ctrl_status']
  _slot_types = ['uint16','int16','uint8']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       name,cur_tor,ctrl_status

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(SetMotorCurTor, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.name is None:
        self.name = 0
      if self.cur_tor is None:
        self.cur_tor = 0
      if self.ctrl_status is None:
        self.ctrl_status = 0
    else:
      self.name = 0
      self.cur_tor = 0
      self.ctrl_status = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_HhB().pack(_x.name, _x.cur_tor, _x.ctrl_status))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 5
      (_x.name, _x.cur_tor, _x.ctrl_status,) = _get_struct_HhB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_HhB().pack(_x.name, _x.cur_tor, _x.ctrl_status))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 5
      (_x.name, _x.cur_tor, _x.ctrl_status,) = _get_struct_HhB().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_HhB = None
def _get_struct_HhB():
    global _struct_HhB
    if _struct_HhB is None:
        _struct_HhB = struct.Struct("<HhB")
    return _struct_HhB
