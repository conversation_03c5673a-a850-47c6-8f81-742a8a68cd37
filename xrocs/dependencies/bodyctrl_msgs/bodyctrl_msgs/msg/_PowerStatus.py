# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/PowerStatus.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class PowerStatus(genpy.Message):
  _md5sum = "c60fc8cd5976555df3f649702e3ee30d"
  _type = "bodyctrl_msgs/PowerStatus"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# @brief 电源板电压、电流、电量、温度等信息

std_msgs/Header header

# temperature information
float32 waist_temp # 腰部MOS温度
float32 arm_a_temp # 臂A MOS温度
float32 arm_b_temp # 臂B MOS温度
float32 leg_a_temp # 腿A MOS温度
float32 leg_b_temp # 腿B MOS温度

float32 waist_temp_max # 腰部MOS温度最大值
float32 arm_a_temp_max # 臂A MOS温度最大值
float32 arm_b_temp_max # 臂B MOS温度最大值
float32 leg_a_temp_max # 腿A MOS温度最大值
float32 leg_b_temp_max # 腿B MOS温度最大值

float32 waist_temp_min # 腰部MOS温度最小值
float32 arm_a_temp_min # 臂A MOS温度最小值
float32 arm_b_temp_min # 臂B MOS温度最小值
float32 leg_a_temp_min # 腿A MOS温度最小值
float32 leg_b_temp_min # 腿B MOS温度最小值

# current information
float32 arm_a_curr # 臂A电流
float32 arm_b_curr # 臂B电流
float32 leg_a_curr # 腿A电流
float32 leg_b_curr # 腿B电流
float32 waist_curr # 腰部电流
float32 head_curr  # 头部电流

float32 arm_a_curr_max # 臂A电流最大值
float32 arm_b_curr_max # 臂B电流最大值
float32 leg_a_curr_max # 腿A电流最大值
float32 leg_b_curr_max # 腿B电流最大值
float32 waist_curr_max # 腰部电流最大值
float32 head_curr_max  # 头部电流最大值

float32 arm_a_curr_min # 臂A电流最小值
float32 arm_b_curr_min # 臂B电流最小值
float32 leg_a_curr_min # 腿A电流最小值
float32 leg_b_curr_min # 腿B电流最小值
float32 waist_curr_min # 腰部电流最小值
float32 head_curr_min  # 头部电流最小值

# version information
string software_version # 软件版本
string hardware_version # 硬件版本

# battery status information
float32 battery_voltage # 电池电压信息
float32 battery_current # 电池电流信息
float32 battery_power   # 电池电量


================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id
"""
  __slots__ = ['header','waist_temp','arm_a_temp','arm_b_temp','leg_a_temp','leg_b_temp','waist_temp_max','arm_a_temp_max','arm_b_temp_max','leg_a_temp_max','leg_b_temp_max','waist_temp_min','arm_a_temp_min','arm_b_temp_min','leg_a_temp_min','leg_b_temp_min','arm_a_curr','arm_b_curr','leg_a_curr','leg_b_curr','waist_curr','head_curr','arm_a_curr_max','arm_b_curr_max','leg_a_curr_max','leg_b_curr_max','waist_curr_max','head_curr_max','arm_a_curr_min','arm_b_curr_min','leg_a_curr_min','leg_b_curr_min','waist_curr_min','head_curr_min','software_version','hardware_version','battery_voltage','battery_current','battery_power']
  _slot_types = ['std_msgs/Header','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','float32','string','string','float32','float32','float32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,waist_temp,arm_a_temp,arm_b_temp,leg_a_temp,leg_b_temp,waist_temp_max,arm_a_temp_max,arm_b_temp_max,leg_a_temp_max,leg_b_temp_max,waist_temp_min,arm_a_temp_min,arm_b_temp_min,leg_a_temp_min,leg_b_temp_min,arm_a_curr,arm_b_curr,leg_a_curr,leg_b_curr,waist_curr,head_curr,arm_a_curr_max,arm_b_curr_max,leg_a_curr_max,leg_b_curr_max,waist_curr_max,head_curr_max,arm_a_curr_min,arm_b_curr_min,leg_a_curr_min,leg_b_curr_min,waist_curr_min,head_curr_min,software_version,hardware_version,battery_voltage,battery_current,battery_power

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(PowerStatus, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.waist_temp is None:
        self.waist_temp = 0.
      if self.arm_a_temp is None:
        self.arm_a_temp = 0.
      if self.arm_b_temp is None:
        self.arm_b_temp = 0.
      if self.leg_a_temp is None:
        self.leg_a_temp = 0.
      if self.leg_b_temp is None:
        self.leg_b_temp = 0.
      if self.waist_temp_max is None:
        self.waist_temp_max = 0.
      if self.arm_a_temp_max is None:
        self.arm_a_temp_max = 0.
      if self.arm_b_temp_max is None:
        self.arm_b_temp_max = 0.
      if self.leg_a_temp_max is None:
        self.leg_a_temp_max = 0.
      if self.leg_b_temp_max is None:
        self.leg_b_temp_max = 0.
      if self.waist_temp_min is None:
        self.waist_temp_min = 0.
      if self.arm_a_temp_min is None:
        self.arm_a_temp_min = 0.
      if self.arm_b_temp_min is None:
        self.arm_b_temp_min = 0.
      if self.leg_a_temp_min is None:
        self.leg_a_temp_min = 0.
      if self.leg_b_temp_min is None:
        self.leg_b_temp_min = 0.
      if self.arm_a_curr is None:
        self.arm_a_curr = 0.
      if self.arm_b_curr is None:
        self.arm_b_curr = 0.
      if self.leg_a_curr is None:
        self.leg_a_curr = 0.
      if self.leg_b_curr is None:
        self.leg_b_curr = 0.
      if self.waist_curr is None:
        self.waist_curr = 0.
      if self.head_curr is None:
        self.head_curr = 0.
      if self.arm_a_curr_max is None:
        self.arm_a_curr_max = 0.
      if self.arm_b_curr_max is None:
        self.arm_b_curr_max = 0.
      if self.leg_a_curr_max is None:
        self.leg_a_curr_max = 0.
      if self.leg_b_curr_max is None:
        self.leg_b_curr_max = 0.
      if self.waist_curr_max is None:
        self.waist_curr_max = 0.
      if self.head_curr_max is None:
        self.head_curr_max = 0.
      if self.arm_a_curr_min is None:
        self.arm_a_curr_min = 0.
      if self.arm_b_curr_min is None:
        self.arm_b_curr_min = 0.
      if self.leg_a_curr_min is None:
        self.leg_a_curr_min = 0.
      if self.leg_b_curr_min is None:
        self.leg_b_curr_min = 0.
      if self.waist_curr_min is None:
        self.waist_curr_min = 0.
      if self.head_curr_min is None:
        self.head_curr_min = 0.
      if self.software_version is None:
        self.software_version = ''
      if self.hardware_version is None:
        self.hardware_version = ''
      if self.battery_voltage is None:
        self.battery_voltage = 0.
      if self.battery_current is None:
        self.battery_current = 0.
      if self.battery_power is None:
        self.battery_power = 0.
    else:
      self.header = std_msgs.msg.Header()
      self.waist_temp = 0.
      self.arm_a_temp = 0.
      self.arm_b_temp = 0.
      self.leg_a_temp = 0.
      self.leg_b_temp = 0.
      self.waist_temp_max = 0.
      self.arm_a_temp_max = 0.
      self.arm_b_temp_max = 0.
      self.leg_a_temp_max = 0.
      self.leg_b_temp_max = 0.
      self.waist_temp_min = 0.
      self.arm_a_temp_min = 0.
      self.arm_b_temp_min = 0.
      self.leg_a_temp_min = 0.
      self.leg_b_temp_min = 0.
      self.arm_a_curr = 0.
      self.arm_b_curr = 0.
      self.leg_a_curr = 0.
      self.leg_b_curr = 0.
      self.waist_curr = 0.
      self.head_curr = 0.
      self.arm_a_curr_max = 0.
      self.arm_b_curr_max = 0.
      self.leg_a_curr_max = 0.
      self.leg_b_curr_max = 0.
      self.waist_curr_max = 0.
      self.head_curr_max = 0.
      self.arm_a_curr_min = 0.
      self.arm_b_curr_min = 0.
      self.leg_a_curr_min = 0.
      self.leg_b_curr_min = 0.
      self.waist_curr_min = 0.
      self.head_curr_min = 0.
      self.software_version = ''
      self.hardware_version = ''
      self.battery_voltage = 0.
      self.battery_current = 0.
      self.battery_power = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_33f().pack(_x.waist_temp, _x.arm_a_temp, _x.arm_b_temp, _x.leg_a_temp, _x.leg_b_temp, _x.waist_temp_max, _x.arm_a_temp_max, _x.arm_b_temp_max, _x.leg_a_temp_max, _x.leg_b_temp_max, _x.waist_temp_min, _x.arm_a_temp_min, _x.arm_b_temp_min, _x.leg_a_temp_min, _x.leg_b_temp_min, _x.arm_a_curr, _x.arm_b_curr, _x.leg_a_curr, _x.leg_b_curr, _x.waist_curr, _x.head_curr, _x.arm_a_curr_max, _x.arm_b_curr_max, _x.leg_a_curr_max, _x.leg_b_curr_max, _x.waist_curr_max, _x.head_curr_max, _x.arm_a_curr_min, _x.arm_b_curr_min, _x.leg_a_curr_min, _x.leg_b_curr_min, _x.waist_curr_min, _x.head_curr_min))
      _x = self.software_version
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.hardware_version
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3f().pack(_x.battery_voltage, _x.battery_current, _x.battery_power))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 132
      (_x.waist_temp, _x.arm_a_temp, _x.arm_b_temp, _x.leg_a_temp, _x.leg_b_temp, _x.waist_temp_max, _x.arm_a_temp_max, _x.arm_b_temp_max, _x.leg_a_temp_max, _x.leg_b_temp_max, _x.waist_temp_min, _x.arm_a_temp_min, _x.arm_b_temp_min, _x.leg_a_temp_min, _x.leg_b_temp_min, _x.arm_a_curr, _x.arm_b_curr, _x.leg_a_curr, _x.leg_b_curr, _x.waist_curr, _x.head_curr, _x.arm_a_curr_max, _x.arm_b_curr_max, _x.leg_a_curr_max, _x.leg_b_curr_max, _x.waist_curr_max, _x.head_curr_max, _x.arm_a_curr_min, _x.arm_b_curr_min, _x.leg_a_curr_min, _x.leg_b_curr_min, _x.waist_curr_min, _x.head_curr_min,) = _get_struct_33f().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.software_version = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.software_version = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.hardware_version = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.hardware_version = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.battery_voltage, _x.battery_current, _x.battery_power,) = _get_struct_3f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_33f().pack(_x.waist_temp, _x.arm_a_temp, _x.arm_b_temp, _x.leg_a_temp, _x.leg_b_temp, _x.waist_temp_max, _x.arm_a_temp_max, _x.arm_b_temp_max, _x.leg_a_temp_max, _x.leg_b_temp_max, _x.waist_temp_min, _x.arm_a_temp_min, _x.arm_b_temp_min, _x.leg_a_temp_min, _x.leg_b_temp_min, _x.arm_a_curr, _x.arm_b_curr, _x.leg_a_curr, _x.leg_b_curr, _x.waist_curr, _x.head_curr, _x.arm_a_curr_max, _x.arm_b_curr_max, _x.leg_a_curr_max, _x.leg_b_curr_max, _x.waist_curr_max, _x.head_curr_max, _x.arm_a_curr_min, _x.arm_b_curr_min, _x.leg_a_curr_min, _x.leg_b_curr_min, _x.waist_curr_min, _x.head_curr_min))
      _x = self.software_version
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self.hardware_version
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_3f().pack(_x.battery_voltage, _x.battery_current, _x.battery_power))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 132
      (_x.waist_temp, _x.arm_a_temp, _x.arm_b_temp, _x.leg_a_temp, _x.leg_b_temp, _x.waist_temp_max, _x.arm_a_temp_max, _x.arm_b_temp_max, _x.leg_a_temp_max, _x.leg_b_temp_max, _x.waist_temp_min, _x.arm_a_temp_min, _x.arm_b_temp_min, _x.leg_a_temp_min, _x.leg_b_temp_min, _x.arm_a_curr, _x.arm_b_curr, _x.leg_a_curr, _x.leg_b_curr, _x.waist_curr, _x.head_curr, _x.arm_a_curr_max, _x.arm_b_curr_max, _x.leg_a_curr_max, _x.leg_b_curr_max, _x.waist_curr_max, _x.head_curr_max, _x.arm_a_curr_min, _x.arm_b_curr_min, _x.leg_a_curr_min, _x.leg_b_curr_min, _x.waist_curr_min, _x.head_curr_min,) = _get_struct_33f().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.software_version = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.software_version = str[start:end]
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.hardware_version = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.hardware_version = str[start:end]
      _x = self
      start = end
      end += 12
      (_x.battery_voltage, _x.battery_current, _x.battery_power,) = _get_struct_3f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_33f = None
def _get_struct_33f():
    global _struct_33f
    if _struct_33f is None:
        _struct_33f = struct.Struct("<33f")
    return _struct_33f
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_3f = None
def _get_struct_3f():
    global _struct_3f
    if _struct_3f is None:
        _struct_3f = struct.Struct("<3f")
    return _struct_3f
