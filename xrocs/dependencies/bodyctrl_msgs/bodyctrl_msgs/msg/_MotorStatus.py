# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/MotorStatus.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class MotorStatus(genpy.Message):
  _md5sum = "a9a27996c0a0af9eb573617caa1fa3f4"
  _type = "bodyctrl_msgs/MotorStatus"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """uint16 name # MotorName
float32 pos   # rad
float32 speed   # rad
float32 current # A
float32 temperature
uint32 error

"""
  __slots__ = ['name','pos','speed','current','temperature','error']
  _slot_types = ['uint16','float32','float32','float32','float32','uint32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       name,pos,speed,current,temperature,error

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(MotorStatus, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.name is None:
        self.name = 0
      if self.pos is None:
        self.pos = 0.
      if self.speed is None:
        self.speed = 0.
      if self.current is None:
        self.current = 0.
      if self.temperature is None:
        self.temperature = 0.
      if self.error is None:
        self.error = 0
    else:
      self.name = 0
      self.pos = 0.
      self.speed = 0.
      self.current = 0.
      self.temperature = 0.
      self.error = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_H4fI().pack(_x.name, _x.pos, _x.speed, _x.current, _x.temperature, _x.error))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 22
      (_x.name, _x.pos, _x.speed, _x.current, _x.temperature, _x.error,) = _get_struct_H4fI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_H4fI().pack(_x.name, _x.pos, _x.speed, _x.current, _x.temperature, _x.error))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 22
      (_x.name, _x.pos, _x.speed, _x.current, _x.temperature, _x.error,) = _get_struct_H4fI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_H4fI = None
def _get_struct_H4fI():
    global _struct_H4fI
    if _struct_H4fI is None:
        _struct_H4fI = struct.Struct("<H4fI")
    return _struct_H4fI
