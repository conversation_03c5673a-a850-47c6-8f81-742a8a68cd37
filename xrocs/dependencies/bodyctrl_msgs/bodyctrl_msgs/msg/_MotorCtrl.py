# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/MotorCtrl.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class MotorCtrl(genpy.Message):
  _md5sum = "e7f320d42512a6f41a9b9433286a1fbb"
  _type = "bodyctrl_msgs/MotorCtrl"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# MotorName
uint16 name
float32 kp
float32 kd
float32 pos
float32 spd
float32 tor"""
  __slots__ = ['name','kp','kd','pos','spd','tor']
  _slot_types = ['uint16','float32','float32','float32','float32','float32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       name,kp,kd,pos,spd,tor

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(MotorCtrl, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.name is None:
        self.name = 0
      if self.kp is None:
        self.kp = 0.
      if self.kd is None:
        self.kd = 0.
      if self.pos is None:
        self.pos = 0.
      if self.spd is None:
        self.spd = 0.
      if self.tor is None:
        self.tor = 0.
    else:
      self.name = 0
      self.kp = 0.
      self.kd = 0.
      self.pos = 0.
      self.spd = 0.
      self.tor = 0.

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_H5f().pack(_x.name, _x.kp, _x.kd, _x.pos, _x.spd, _x.tor))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 22
      (_x.name, _x.kp, _x.kd, _x.pos, _x.spd, _x.tor,) = _get_struct_H5f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_H5f().pack(_x.name, _x.kp, _x.kd, _x.pos, _x.spd, _x.tor))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 22
      (_x.name, _x.kp, _x.kd, _x.pos, _x.spd, _x.tor,) = _get_struct_H5f().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_H5f = None
def _get_struct_H5f():
    global _struct_H5f
    if _struct_H5f is None:
        _struct_H5f = struct.Struct("<H5f")
    return _struct_H5f
