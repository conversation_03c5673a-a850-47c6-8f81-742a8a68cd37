# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/TsHandStatus.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct


class TsHandStatus(genpy.Message):
  _md5sum = "357e49c1bc9a872e5c22112489e77611"
  _type = "bodyctrl_msgs/TsHandStatus"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# type TsHandName
int32 name

# thumb
# range: 0 ~ 90
uint16 rotation_angle

# 0 - thumb, 1 - fore, 2 - middle, 3 - ring, 4 - little
# range: 0 ~ 90
uint16[] bend_angle """
  __slots__ = ['name','rotation_angle','bend_angle']
  _slot_types = ['int32','uint16','uint16[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       name,rotation_angle,bend_angle

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(TsHandStatus, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.name is None:
        self.name = 0
      if self.rotation_angle is None:
        self.rotation_angle = 0
      if self.bend_angle is None:
        self.bend_angle = []
    else:
      self.name = 0
      self.rotation_angle = 0
      self.bend_angle = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_iH().pack(_x.name, _x.rotation_angle))
      length = len(self.bend_angle)
      buff.write(_struct_I.pack(length))
      pattern = '<%sH'%length
      buff.write(struct.Struct(pattern).pack(*self.bend_angle))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 6
      (_x.name, _x.rotation_angle,) = _get_struct_iH().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sH'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.bend_angle = s.unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_iH().pack(_x.name, _x.rotation_angle))
      length = len(self.bend_angle)
      buff.write(_struct_I.pack(length))
      pattern = '<%sH'%length
      buff.write(self.bend_angle.tostring())
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      end = 0
      _x = self
      start = end
      end += 6
      (_x.name, _x.rotation_angle,) = _get_struct_iH().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sH'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.bend_angle = numpy.frombuffer(str[start:end], dtype=numpy.uint16, count=length)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_iH = None
def _get_struct_iH():
    global _struct_iH
    if _struct_iH is None:
        _struct_iH = struct.Struct("<iH")
    return _struct_iH
