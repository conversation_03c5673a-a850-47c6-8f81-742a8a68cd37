# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/SetTsHandCtrl.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import bodyctrl_msgs.msg

class SetTsHandCtrl(genpy.Message):
  _md5sum = "b17f71b6f1d727d40893b70658160450"
  _type = "bodyctrl_msgs/SetTsHandCtrl"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """# type TsHandName
int32 name

# thumb
SetTsHandCtrlItem rotation

# 0 - thumb, 1 - fore, 2 - middle, 3 - ring, 4 - little
SetTsHandCtrlItem[] bend

# 0 - thumb, 1 - fore, 2 - middle, 3 - ring, 4 - little
# range: 20 ~ 1000
uint16[] threshold
================================================================================
MSG: bodyctrl_msgs/SetTsHandCtrlItem
# range: 20 ~ 200
uint16 vel

# range: 0 ~ 90
uint16 start_angle

# range: 0 ~ 90
uint16 max_angle"""
  __slots__ = ['name','rotation','bend','threshold']
  _slot_types = ['int32','bodyctrl_msgs/SetTsHandCtrlItem','bodyctrl_msgs/SetTsHandCtrlItem[]','uint16[]']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       name,rotation,bend,threshold

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(SetTsHandCtrl, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.name is None:
        self.name = 0
      if self.rotation is None:
        self.rotation = bodyctrl_msgs.msg.SetTsHandCtrlItem()
      if self.bend is None:
        self.bend = []
      if self.threshold is None:
        self.threshold = []
    else:
      self.name = 0
      self.rotation = bodyctrl_msgs.msg.SetTsHandCtrlItem()
      self.bend = []
      self.threshold = []

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_i3H().pack(_x.name, _x.rotation.vel, _x.rotation.start_angle, _x.rotation.max_angle))
      length = len(self.bend)
      buff.write(_struct_I.pack(length))
      for val1 in self.bend:
        _x = val1
        buff.write(_get_struct_3H().pack(_x.vel, _x.start_angle, _x.max_angle))
      length = len(self.threshold)
      buff.write(_struct_I.pack(length))
      pattern = '<%sH'%length
      buff.write(struct.Struct(pattern).pack(*self.threshold))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.rotation is None:
        self.rotation = bodyctrl_msgs.msg.SetTsHandCtrlItem()
      if self.bend is None:
        self.bend = None
      end = 0
      _x = self
      start = end
      end += 10
      (_x.name, _x.rotation.vel, _x.rotation.start_angle, _x.rotation.max_angle,) = _get_struct_i3H().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.bend = []
      for i in range(0, length):
        val1 = bodyctrl_msgs.msg.SetTsHandCtrlItem()
        _x = val1
        start = end
        end += 6
        (_x.vel, _x.start_angle, _x.max_angle,) = _get_struct_3H().unpack(str[start:end])
        self.bend.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sH'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.threshold = s.unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_i3H().pack(_x.name, _x.rotation.vel, _x.rotation.start_angle, _x.rotation.max_angle))
      length = len(self.bend)
      buff.write(_struct_I.pack(length))
      for val1 in self.bend:
        _x = val1
        buff.write(_get_struct_3H().pack(_x.vel, _x.start_angle, _x.max_angle))
      length = len(self.threshold)
      buff.write(_struct_I.pack(length))
      pattern = '<%sH'%length
      buff.write(self.threshold.tostring())
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.rotation is None:
        self.rotation = bodyctrl_msgs.msg.SetTsHandCtrlItem()
      if self.bend is None:
        self.bend = None
      end = 0
      _x = self
      start = end
      end += 10
      (_x.name, _x.rotation.vel, _x.rotation.start_angle, _x.rotation.max_angle,) = _get_struct_i3H().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      self.bend = []
      for i in range(0, length):
        val1 = bodyctrl_msgs.msg.SetTsHandCtrlItem()
        _x = val1
        start = end
        end += 6
        (_x.vel, _x.start_angle, _x.max_angle,) = _get_struct_3H().unpack(str[start:end])
        self.bend.append(val1)
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      pattern = '<%sH'%length
      start = end
      s = struct.Struct(pattern)
      end += s.size
      self.threshold = numpy.frombuffer(str[start:end], dtype=numpy.uint16, count=length)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3H = None
def _get_struct_3H():
    global _struct_3H
    if _struct_3H is None:
        _struct_3H = struct.Struct("<3H")
    return _struct_3H
_struct_i3H = None
def _get_struct_i3H():
    global _struct_i3H
    if _struct_i3H is None:
        _struct_i3H = struct.Struct("<i3H")
    return _struct_i3H
