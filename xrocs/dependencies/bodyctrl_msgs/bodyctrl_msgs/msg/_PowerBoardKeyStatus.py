# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from bodyctrl_msgs/PowerBoardKeyStatus.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import std_msgs.msg

class PowerBoardKeyStatus(genpy.Message):
  _md5sum = "76af17f374ebc87c391633823401434b"
  _type = "bodyctrl_msgs/PowerBoardKeyStatus"
  _has_header = True  # flag to mark the presence of a Header object
  _full_text = """# @brief 电源板按键状态

std_msgs/Header header

# power board status
uint32 work_time
std_msgs/Bool is_estop
std_msgs/Bool is_remote_estop
std_msgs/Bool is_power_on


================================================================================
MSG: std_msgs/Header
# Standard metadata for higher-level stamped data types.
# This is generally used to communicate timestamped data 
# in a particular coordinate frame.
# 
# sequence ID: consecutively increasing ID 
uint32 seq
#Two-integer timestamp that is expressed as:
# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')
# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')
# time-handling sugar is provided by the client library
time stamp
#Frame this data is associated with
string frame_id

================================================================================
MSG: std_msgs/Bool
bool data"""
  __slots__ = ['header','work_time','is_estop','is_remote_estop','is_power_on']
  _slot_types = ['std_msgs/Header','uint32','std_msgs/Bool','std_msgs/Bool','std_msgs/Bool']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       header,work_time,is_estop,is_remote_estop,is_power_on

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(PowerBoardKeyStatus, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.work_time is None:
        self.work_time = 0
      if self.is_estop is None:
        self.is_estop = std_msgs.msg.Bool()
      if self.is_remote_estop is None:
        self.is_remote_estop = std_msgs.msg.Bool()
      if self.is_power_on is None:
        self.is_power_on = std_msgs.msg.Bool()
    else:
      self.header = std_msgs.msg.Header()
      self.work_time = 0
      self.is_estop = std_msgs.msg.Bool()
      self.is_remote_estop = std_msgs.msg.Bool()
      self.is_power_on = std_msgs.msg.Bool()

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_I3B().pack(_x.work_time, _x.is_estop.data, _x.is_remote_estop.data, _x.is_power_on.data))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.is_estop is None:
        self.is_estop = std_msgs.msg.Bool()
      if self.is_remote_estop is None:
        self.is_remote_estop = std_msgs.msg.Bool()
      if self.is_power_on is None:
        self.is_power_on = std_msgs.msg.Bool()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 7
      (_x.work_time, _x.is_estop.data, _x.is_remote_estop.data, _x.is_power_on.data,) = _get_struct_I3B().unpack(str[start:end])
      self.is_estop.data = bool(self.is_estop.data)
      self.is_remote_estop.data = bool(self.is_remote_estop.data)
      self.is_power_on.data = bool(self.is_power_on.data)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_3I().pack(_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs))
      _x = self.header.frame_id
      length = len(_x)
      if python3 or type(_x) == unicode:
        _x = _x.encode('utf-8')
        length = len(_x)
      buff.write(struct.Struct('<I%ss'%length).pack(length, _x))
      _x = self
      buff.write(_get_struct_I3B().pack(_x.work_time, _x.is_estop.data, _x.is_remote_estop.data, _x.is_power_on.data))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.header is None:
        self.header = std_msgs.msg.Header()
      if self.is_estop is None:
        self.is_estop = std_msgs.msg.Bool()
      if self.is_remote_estop is None:
        self.is_remote_estop = std_msgs.msg.Bool()
      if self.is_power_on is None:
        self.is_power_on = std_msgs.msg.Bool()
      end = 0
      _x = self
      start = end
      end += 12
      (_x.header.seq, _x.header.stamp.secs, _x.header.stamp.nsecs,) = _get_struct_3I().unpack(str[start:end])
      start = end
      end += 4
      (length,) = _struct_I.unpack(str[start:end])
      start = end
      end += length
      if python3:
        self.header.frame_id = str[start:end].decode('utf-8', 'rosmsg')
      else:
        self.header.frame_id = str[start:end]
      _x = self
      start = end
      end += 7
      (_x.work_time, _x.is_estop.data, _x.is_remote_estop.data, _x.is_power_on.data,) = _get_struct_I3B().unpack(str[start:end])
      self.is_estop.data = bool(self.is_estop.data)
      self.is_remote_estop.data = bool(self.is_remote_estop.data)
      self.is_power_on.data = bool(self.is_power_on.data)
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_3I = None
def _get_struct_3I():
    global _struct_3I
    if _struct_3I is None:
        _struct_3I = struct.Struct("<3I")
    return _struct_3I
_struct_I3B = None
def _get_struct_I3B():
    global _struct_I3B
    if _struct_I3B is None:
        _struct_I3B = struct.Struct("<I3B")
    return _struct_I3B
