try:
    from typing import Dict, <PERSON>, <PERSON>tional, <PERSON><PERSON>, <PERSON>V<PERSON>, Literal
    from xrocs.core.config_loader import config
    from common.data_type import Coordinate, CoordinateArray, Pose, PoseArray, Rotation
    from xrocs.utils.logger.logger_loader import logger

    __all__ = ['FrankaPoseTransformer']
except Exception:
    import traceback
    print(traceback.format_exc())


_CS = Coordinate.CoordinateSystem
_TTransformableData = TypeVar('_TTransformableData', Coordinate, CoordinateArray, Pose, PoseArray)


class FrankaPoseTransformer:
    def __init__(self, side: Literal['left_head', 'right_head'] = 'left_head') -> None:
        self._config = config['common']['franka_pose_transformer']

        self._T: Dict[Tuple[_CS, _CS], Optional[Pose]]

        self._side = side

        self._init_trans()

        self._previous_end_effector_pose: Optional[Pose] = None

    @staticmethod
    def _get_inverse_pose(pose: Po<PERSON>, new_coordinate_system: _CS) -> Pose:
        """ P_a(b) -> P_b(a).
        Args:
            pose:
            new_coordinate_system:

        Returns:
            The inverse pose.
        """
        # | R C |  x  | R^T -R^{T}C |  =  | RR^T -RR^{T}C+C |  =  I
        # | 0 1 |     | 0     1     |     |  0        1     |
        R_T = pose.rotation.get_matrix_ndarray().T
        new_rotation = Rotation(R_T, rotation_form_type=Rotation.FormType.MATRIX)
        new_coordinate = Coordinate(-R_T @ pose.coordinate.get_xyz_m_ndarray(),
                                    length_unit_type=Coordinate.LengthUnitType.METER,
                                    coordinate_system_type=new_coordinate_system)
        return Pose(new_coordinate, new_rotation)

    def _init_trans(self) -> None:
        # 1. get fixed adjacent transforms
        handeye_camera_to_end_effector = Pose.from_affine_matrix(self._config['hand_camera_to_end_effector'],
                                                                 coordinate_system_type=_CS.ARM_END_EFFECTOR,
                                                                 length_unit_type=Coordinate.LengthUnitType.METER)
        if self._side == 'left_head':
            head_camera_to_head_base = Pose.from_affine_matrix(self._config['left_head_camera_to_head_base'],
                                                               coordinate_system_type=_CS.HEAD_BASE,
                                                               length_unit_type=Coordinate.LengthUnitType.METER)
        else:
            head_camera_to_head_base = Pose.from_affine_matrix(self._config['left_head_camera_to_head_base'],
                                                               coordinate_system_type=_CS.HEAD_BASE,
                                                               length_unit_type=Coordinate.LengthUnitType.METER)

        # 2. determine all adjacent transforms
        # _T[(CS1, CS2)] is to transform cs from CS1 to CS2, is Pose_{CS2}(CS1), is named CS1_to_CS2
        self._T = {
            (_CS.ARM_END_EFFECTOR, _CS.ARM_BASE): None,  # query: end_effector_pose
            (_CS.HANDEYE_CAMERA, _CS.ARM_END_EFFECTOR): handeye_camera_to_end_effector,
            (_CS.HEAD_BASE, _CS.ARM_BASE): None,  # query: body_data.head_xxx
            (_CS.HEAD_CAMERA, _CS.HEAD_BASE): head_camera_to_head_base,
        }

        system_pairs = tuple(self._T.keys())  # To freeze the keys during the following for-loop
        for pair in system_pairs:
            cs1, cs2 = pair
            new_pair = (cs2, cs1)
            pose = self._T[pair]
            self._T[new_pair] = None if pose is None else self._get_inverse_pose(pose, cs1)

        # 3. get fixed intermediate transforms
        self._NECK_JOINT_TO_ARM_BASE = Pose.from_affine_matrix(self._config['neck_joint_to_arm_base'],
                                                               coordinate_system_type=_CS.ARM_BASE,
                                                               length_unit_type=Coordinate.LengthUnitType.METER)
        self._HEAD_JOINT_TO_NECK_BASE = Pose.from_affine_matrix(self._config['head_joint_to_neck_base'],
                                                                coordinate_system_type=_CS.NONE,
                                                                length_unit_type=Coordinate.LengthUnitType.METER)

    def _update_trans(self,
                      transform_path: List[Tuple[_CS, _CS]],
                      end_effector_pose_in_arm_base: Optional[Pose] = None) -> None:
        if (_CS.ARM_END_EFFECTOR, _CS.ARM_BASE) in transform_path or (_CS.ARM_BASE,
                                                                      _CS.ARM_END_EFFECTOR) in transform_path:
            if end_effector_pose_in_arm_base is None:
                raise RuntimeError('No enough info for coordinate system transform (ARM_END_EFFECTOR, ARM_BASE):'
                                   'end_effector_pose_in_arm_base should be provided.')
            if self._previous_end_effector_pose != end_effector_pose_in_arm_base:
                self._previous_end_effector_pose = end_effector_pose_in_arm_base
                # end_effector_pose_in_arm_base is the pose of end_effector_in_arm_base
                self._T[(_CS.ARM_END_EFFECTOR, _CS.ARM_BASE)] = end_effector_pose_in_arm_base
                if (_CS.ARM_BASE, _CS.ARM_END_EFFECTOR) in transform_path:
                    self._T[(_CS.ARM_BASE,
                             _CS.ARM_END_EFFECTOR)] = self._get_inverse_pose(end_effector_pose_in_arm_base,
                                                                             _CS.ARM_END_EFFECTOR)
            else:  # self._previous_end_effector_pose == end_effector_pose_in_arm_base
                logger.debug('Input end effector pose equals the previous one: '
                             f'Previous: {self._previous_end_effector_pose}; Input: {end_effector_pose_in_arm_base}')

        if (_CS.HEAD_BASE, _CS.ARM_BASE) in transform_path or (_CS.ARM_BASE, _CS.HEAD_BASE) in transform_path:
            # body_data contains head_pitch and head_rotation
            # ARM_BASE -- NECK_JOINT -(yaw)- NECK_BASE -- HEAD_JOINT -(pitch)- HEAD_BASE
            p_neck_base_to_neck_joint = Pose(
                Coordinate([0., 0., 0.], coordinate_system_type=_CS.NONE),
                Rotation([0., 0., 0.], angle_unit_type=Rotation.AngleUnitType.RADIAN))
            # The head pitch on EVT robot is reversed to our head frame: therefore '-' is needed.
            # TODO(Joy): change head_pitch direction after using the new robot usd!!!
            p_head_base_to_head_joint = Pose(
                Coordinate([0., 0., 0.], coordinate_system_type=_CS.NONE),
                Rotation([0., 0., 0.], angle_unit_type=Rotation.AngleUnitType.RADIAN))
            p_head_base_to_arm_base = p_head_base_to_head_joint.transform_coordinate_system(
                self._HEAD_JOINT_TO_NECK_BASE).transform_coordinate_system(
                    p_neck_base_to_neck_joint).transform_coordinate_system(self._NECK_JOINT_TO_ARM_BASE)
            self._T[(_CS.HEAD_BASE, _CS.ARM_BASE)] = p_head_base_to_arm_base
            if (_CS.ARM_BASE, _CS.HEAD_BASE) in transform_path:
                self._T[(_CS.ARM_BASE,
                         _CS.HEAD_BASE)] = self._get_inverse_pose(p_head_base_to_arm_base, _CS.HEAD_BASE)

    def _reset_trans(self) -> None:
        for cs1, cs2 in [(_CS.ARM_BASE, _CS.ARM_END_EFFECTOR),
                         (_CS.ARM_BASE, _CS.HEAD_BASE)]:
            self._T[(cs1, cs2)] = None
            self._T[(cs2, cs1)] = None

    def _get_transform_path(self, original_system: _CS, target_system: _CS) -> List[Tuple[_CS, _CS]]:
        """Given two coordinate systems, find the transform path from original to target.

        [ARM_BASE] ---- ARM_END_EFFECTOR ---- HANDEYE_CAMERA
           |
         HEAD_BASE ---- HEAD_CAMERA

        Args:
            original_system:
            target_system:

        Returns:
        """
        # Case-1: adjacent coordinate systems: return the direct transform
        if (original_system, target_system) in self._T.keys():
            return [(original_system, target_system)]
        # Case-2: ARM_BASE is one of the systems: the transform is 2-step, find it recursively
        if original_system == _CS.ARM_BASE or target_system == _CS.ARM_BASE:
            if _CS.HANDEYE_CAMERA in (original_system, target_system):
                transit_system = _CS.ARM_END_EFFECTOR
            elif _CS.HEAD_CAMERA in (original_system, target_system):
                transit_system = _CS.HEAD_BASE
            else:
                raise ValueError(f'Can not find middle coordinate system from {original_system} to {target_system}.')
            return (self._get_transform_path(original_system, transit_system) +
                    self._get_transform_path(transit_system, target_system))
        # Case-3: Other cases: use ARM_BASE as the transit_system, find the transform path recursively
        return (self._get_transform_path(original_system, _CS.ARM_BASE) +
                self._get_transform_path(_CS.ARM_BASE, target_system))

    def _transform_to(self,
                      pose_or_coordinate: _TTransformableData,
                      target_system: _CS,
                      *,
                      end_effector_pose_in_arm_base: Optional[Pose] = None) -> _TTransformableData:
        """Transform pose to the new coordinate_system.

        Args:
            pose_or_coordinate:
            target_system:

        Returns:
            The transformed pose (the pose is not moved, and its axis is changed).
        """
        if isinstance(pose_or_coordinate, CoordinateArray) or isinstance(pose_or_coordinate, PoseArray):
            ret = pose_or_coordinate.__class__()
            for item in pose_or_coordinate:
                ret.append(
                    self._transform_to(item,
                                       target_system,
                                       end_effector_pose_in_arm_base=end_effector_pose_in_arm_base))
            return ret
            # 1. find the transform path from pose's CS (original) to target
        if pose_or_coordinate.system == target_system:
            return pose_or_coordinate
        transform_path = self._get_transform_path(pose_or_coordinate.system, target_system)

        # 2. update transform path's matrix by the input poses if needed
        self._update_trans(transform_path=transform_path,
                           end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

        # 3. iteratively transform from pose's CS (original) to target
        for trans in transform_path:
            t_trans = self._T[trans]
            assert t_trans is not None  # for mypy
            pose_or_coordinate = pose_or_coordinate.transform_coordinate_system(t_trans)

        return pose_or_coordinate

    def to_arm_base(self,
                    pose_or_coordinate: _TTransformableData,
                    *,
                    end_effector_pose_in_arm_base: Optional[Pose] = None,) -> _TTransformableData:
        return self._transform_to(pose_or_coordinate,
                                  _CS.ARM_BASE,
                                  end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

    def to_end_effector(self,
                        pose_or_coordinate: _TTransformableData,
                        *,
                        end_effector_pose_in_arm_base: Optional[Pose] = None) -> _TTransformableData:
        return self._transform_to(pose_or_coordinate,
                                  _CS.ARM_END_EFFECTOR,
                                  end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

    def to_handeye_cam(self,
                       pose_or_coordinate: _TTransformableData,
                       *,
                       end_effector_pose_in_arm_base: Optional[Pose] = None) -> _TTransformableData:
        return self._transform_to(pose_or_coordinate,
                                  _CS.HANDEYE_CAMERA,
                                  end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

    def to_head_base(self,
                     pose_or_coordinate: _TTransformableData,
                     *,
                     end_effector_pose_in_arm_base: Optional[Pose] = None,) -> _TTransformableData:
        return self._transform_to(pose_or_coordinate,
                                  _CS.HEAD_BASE,
                                  end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

    def to_head_camera(self,
                       pose_or_coordinate: _TTransformableData,
                       *,
                       end_effector_pose_in_arm_base: Optional[Pose] = None) -> _TTransformableData:
        return self._transform_to(pose_or_coordinate,
                                  _CS.HEAD_CAMERA,
                                  end_effector_pose_in_arm_base=end_effector_pose_in_arm_base)

if __name__ == '__main__':
    import transforms3d as t3d
    import numpy as np
    try:
        # extrinsic_xyzrpy_head_left = [0.15614421655941382, 0.33236331165000127, 0.7567928009890474, -2.173884837461491, 0.03880314060646928, -2.070493802054894]
        extrinsic_xyzrpy_head_left = [0.1459041141192743, 0.34805353298598846, 0.7562011454847368, -2.1786752268257525, 0.038538445360809304,
         -2.0471534281589094]
        ex_rot_M = t3d.euler.euler2mat(*extrinsic_xyzrpy_head_left[3:])
        extrinsics_matrix44 = t3d.affines.compose(np.asarray(extrinsic_xyzrpy_head_left[:3]), ex_rot_M, np.ones(3), np.zeros(3))

        print(f'left head extrinsics_matrix44: {extrinsics_matrix44}')

        extrinsic_xyzrpy_head_right = [0.4498622835716873, -0.7379580803496321, 0.47123280699320025, -2.0651878662882535, -0.063751093033934,
         -0.793160118960085]
        ex_rot_M = t3d.euler.euler2mat(*extrinsic_xyzrpy_head_right[3:])
        extrinsics_matrix44 = t3d.affines.compose(np.asarray(extrinsic_xyzrpy_head_right[:3]), ex_rot_M, np.ones(3),
                                                  np.zeros(3))

        print(f'right head extrinsics_matrix44: {extrinsics_matrix44}')
    except Exception:
        import traceback
        print(traceback.format_exc())





