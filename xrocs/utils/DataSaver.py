import datetime
import pickle
from pathlib import Path
import h5py


def _save_to_hdf5(file: h5py.File, name: str, data: list):
    """
    Save data to HDF5 file recursively.

    Args:
        file (h5py.File): The HDF5 file object.
        name (str): The base name for the dataset.
        data (list): The data to be saved.

    Returns:
        None
    """
    def save_recursive(base_name, value, index):
        if isinstance(value, dict):
            for key, sub_value in value.items():
                save_recursive(f"{base_name}/{key}", sub_value, index)
        else:
            file.create_dataset(f"{base_name}/i{index}",
                                data=value, compression="gzip",
                                compression_opts=9)

    for i, item in enumerate(data):
        for key, value in item.items():
            save_recursive(f"{name}/{key}", value, i)


def save_trajectory(save_path: Path, observations: list, actions: list) -> None:
    """
    Save trajectory data to the specified path in HDF5 format.

    Args:
        save_path (Path): The directory where the trajectory data will be saved.
        observations (list): A list of observation data.
        actions (list): A list of action data.

    Returns:
        None
    """
    save_path = Path(save_path)
    save_path.mkdir(parents=True, exist_ok=True)

    hdf5_file = save_path / f"{save_path.name}_trajectory.h5"
    with h5py.File(hdf5_file, "w") as file:
        _save_to_hdf5(file, 'observations', observations)
        _save_to_hdf5(file, 'actions', actions)


def save_frame(
    folder: Path,
    timestamp: datetime.datetime,
    obs: dict,
    action: dict,
) -> None:
    obs = obs.copy()
    obs["control"] = action  # add action to obs

    # make folder if it doesn't exist
    folder.mkdir(exist_ok=True, parents=True)
    recorded_file = folder / (timestamp.isoformat() + ".pkl")

    with open(recorded_file, "wb") as f:
        pickle.dump(obs, f)
