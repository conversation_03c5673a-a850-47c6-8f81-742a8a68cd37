import pickle
import time
import numpy as np

class ArmDataRecorder:
    def __init__(self):
        self.file_path = "./data.pkl"
        try:
            with open(self.file_path, 'rb') as f:
                self.data = pickle.load(f)
        except FileNotFoundError:
            self.data = []

    def record_data(self, joint_values):
        timestamp = time.time()
        joint_values_list = joint_values.tolist() if isinstance(joint_values, np.ndarray) else joint_values
        new_record = (timestamp, joint_values_list)
        self.data.append(new_record)
        with open(self.file_path, 'wb') as f:
            pickle.dump(self.data, f)

if __name__ == "__main__":
    recorder = ArmDataRecorder()
    joint_values_1 = np.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6])
    recorder.record_data(joint_values_1)
    joint_values_2 = np.array([0.2, 0.3, 0.4, 0.5, 0.6, 0.7])
    recorder.record_data(joint_values_2)