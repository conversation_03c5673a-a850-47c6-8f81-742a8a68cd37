import os
from typing import Optional
import numpy as np
import transforms3d as t3d
import torch
from curobo.geom.sdf.world import CollisionCheckerType
from curobo.geom.types import WorldConfig
from curobo.types.base import TensorDeviceType
from curobo.types.math import Pose as CuroboPose
from curobo.util_file import load_yaml
from curobo.wrap.reacher.ik_solver import IKSolver, IKSolverConfig

from xrocs.core.config_loader import config
from xrocs.common.logger.logger_loader import logger

tensor_args = TensorDeviceType()


class KinematicSolver:

    def __init__(self):
        super().__init__()
        self.ik_config = config['kinematics']
        robot_cfg = self.ik_config['robot_cfg']
        world_cfg = self.ik_config['world_cfg']
        self.robot_path = self.ik_config['robot_description']

        self.default_joint = None
        self.link_retract_pose = None
        self.ee_link_name = None
        self.robot_cfg = None
        self.world_cfg = None
        self.plan_config = None
        self.joints_name = None
        self.ik_solver = None

        self.load_config(robot_cfg, world_cfg)

    def load_config(
            self,
            robot_cfg: str,
            world_cfg: str,
            collision_check: bool = True,
            obstacle_cuboids_cache: int = 10,
            obstacle_mesh_cache: int = 20,
    ):
        robot_cfg_path = os.path.join(self.robot_path, 'config', robot_cfg)
        world_cfg_path = os.path.join(self.robot_path, 'config', world_cfg)
        self.robot_cfg = load_yaml(robot_cfg_path)['robot_cfg']
        self.ee_link_name = self.robot_cfg['kinematics']['ee_link']
        self.default_joint = self.robot_cfg["kinematics"]["cspace"]["retract_config"]
        urdf_path = self.robot_cfg['kinematics']['urdf_path']
        asset_root_path = '../robot_description' + os.path.dirname(urdf_path)
        self.robot_cfg['kinematics']['urdf_path'] = urdf_path
        self.robot_cfg['kinematics']['asset_root_path'] = asset_root_path
        self.robot_cfg['kinematics']['external_asset_path'] = self.robot_path
        self.robot_cfg['kinematics']['external_robot_configs_path'] = self.robot_path

        world_cfg_table = WorldConfig.from_dict(load_yaml(world_cfg_path))
        world_cfg_table.cuboid[0].pose[2] -= 0.02  # table
        world_cfg_mesh = WorldConfig.from_dict(load_yaml(world_cfg_path)).get_mesh_world()
        world_cfg_mesh.mesh[0].name += '_mesh'
        world_cfg_mesh.mesh[0].pose[2] = -10.5
        self.world_cfg = WorldConfig(cuboid=world_cfg_table.cuboid, mesh=world_cfg_mesh.mesh)

        self.joints_name = self.robot_cfg['kinematics']['cspace']['joint_names']

        ik_config = IKSolverConfig.load_from_robot_config(
            self.robot_cfg,
            self.world_cfg,
            rotation_threshold=0.05,
            position_threshold=0.001,
            num_seeds=20,
            self_collision_check=collision_check,
            self_collision_opt=collision_check,
            tensor_args=tensor_args,
            use_cuda_graph=True,
            collision_checker_type=CollisionCheckerType.MESH,
            collision_cache={
                'obb': obstacle_cuboids_cache,
                'mesh': obstacle_mesh_cache
            },
            # use_fixed_samples=True,
        )
        self.ik_solver = IKSolver(ik_config)

        # Warm Up
        logger.info('IK Warm Up...')
        self.warm_up()
        logger.info('IK Warm Up Done!')

    def warm_up(self):
        """
        Warm up the IK solver by sampling a few random configurations and solving them.
        """
        q_sample = self.ik_solver.sample_configs(1)
        kin_state = self.ik_solver.fk(q_sample)
        ee_pose = CuroboPose(position=tensor_args.to_device(kin_state.ee_position),
                             quaternion=tensor_args.to_device(kin_state.ee_quaternion))
        for i in range(5):
            _ = self.ik_solver.solve_batch(ee_pose)
        torch.cuda.synchronize()

    def forward_kinematics(self, joint_angles: np.ndarray) -> np.ndarray:
        """
        :param joint_angles: unit: radian
        :return: [x, y, z, w, x, y, z]
        """
        state = self.ik_solver.fk(tensor_args.to_device(joint_angles))
        ee_position = state.ee_position.cpu().numpy()[0]
        ee_wxyz = state.ee_quaternion.cpu().numpy()[0]
        return np.concatenate([ee_position, np.concatenate([ee_wxyz[1:], [ee_wxyz[0]]])])

    def inverse_kinematics(self, pose: np.ndarray) -> Optional[np.ndarray]:
        """
        :param pose: [x, y, z, w, x, y, z]
        :return: joint angles , unit: radian
        """
        ee_pose = CuroboPose(position=tensor_args.to_device(pose[:3]),
                             quaternion=tensor_args.to_device(
                                 t3d.euler.euler2quat(*pose[3:], axes='sxyz')))
        result = self.ik_solver.solve_batch(goal_pose=ee_pose)
        torch.cuda.synchronize()
        success = torch.any(result.success)
        if success:
            cmd_plan = result.js_solution
            cmd_plan = cmd_plan.get_ordered_joint_state(self.joints_name)
            result_position = cmd_plan.position.cpu().numpy()[0][0][:7]
            logger.success(f'Ik Solve Success. Solved joints is {self.ik_solver.kinematics.joint_names}.\n'
                           f'Joints angle is {result_position}')
            return result_position
        else:
            logger.warning('Ik solve did not converge to a solution.')
            return None


