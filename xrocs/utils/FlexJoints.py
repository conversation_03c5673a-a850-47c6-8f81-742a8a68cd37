from collections import deque
import numpy as np
import unittest

class Flex:
    _METHOD_PARAMS = {
        'moving_average': ['window_size'],
        'exponential': ['alpha'],
        'weight_average': ['window_size']
    }

    def __init__(self, dof, method='weight_average', **kwargs):
        """
        Flex filter
        
        :param dof: Num of dof (int)
        :param method:  default: 'moving_average' (str) 
        :param kwargs: 
            - moving_average --: window_size
            - exponential --: alpha (default: 0.5)
        """
        self.dof = dof
        self.method = method
        default_window_size = 30

        if method not in self._METHOD_PARAMS:
            raise ValueError(f"Unsupported method: {method}. Valid methods: {list(self._METHOD_PARAMS.keys())}")

        allowed_params = self._METHOD_PARAMS[method]
        for kwarg in kwargs:
            if kwarg not in allowed_params:
                raise TypeError(f"Method '{method}' got unexpected argument '{kwarg}'")

        if method == 'moving_average' or method == 'weight_average':
            self.window_size = kwargs.get('window_size')
            init_state = kwargs.get('init_state')
            if self.window_size is None:
                self.window_size = default_window_size
            self.history = deque(maxlen=self.window_size)
            for i in range(self.window_size):
                self.history.append(init_state)
        elif method == 'exponential':
            self.alpha = kwargs.get('alpha', 0.5)
            if not 0 <= self.alpha <= 1:
                raise ValueError("alpha must be in [0, 1]")
            self.last_smoothed = None

    def update(self, new_data):
        self._validate_data(new_data)
        if self.method == 'moving_average':
            self.history.append(new_data)
            return self._moving_average()
        elif self.method == 'exponential':
            return self._exponential_smoothing(new_data)
        elif self.method == 'weight_average':
            self.history.append(new_data)
            return self._weight_average()

    def _validate_data(self, data):
        if len(data) != self.dof:
            raise ValueError(f"Invalid data dimension. Expected {self.dof}, got {len(data)}")

    def _moving_average(self):
        sum_values = [0.0] * self.dof
        for data_point in self.history:
            for i in range(self.dof):
                sum_values[i] += data_point[i]
        return [sum_val / len(self.history) for sum_val in sum_values]

    def _exponential_smoothing(self, new_data):
        if self.last_smoothed is None:
            self.last_smoothed = list(new_data)
        else:
            smoothed = []
            for i in range(self.dof):
                smoothed_val = self.alpha * new_data[i] + (1 - self.alpha) * self.last_smoothed[i]
                smoothed.append(smoothed_val)
            self.last_smoothed = smoothed
        return self.last_smoothed.copy()
    
    def _weight_average(self):
        x = np.linspace(0, 3.5, self.window_size)
        weights = np.exp(-(x**2) / 2)
        weights = weights[::-1]
        weights = weights / np.sum(weights)
        history_array = np.array(self.history)
        weighted_sum = []
        for i in range(self.dof):
            tmp_sum = sum([w * v for w, v in zip(weights, history_array[:, i])])
            weighted_sum.append(tmp_sum)
        return weighted_sum

    def reset(self):
        if self.method == 'moving_average' or self.method == 'weight_average':
            self.history.clear()
        elif self.method == 'exponential':
            self.last_smoothed = None


class TestFlex(unittest.TestCase):
    def test_weight_average_long(self):
        dof = 7
        window_size = 20
        flex_filter = Flex(dof, method='weight_average', window_size=window_size)
        for _ in range(30):
            new_data = np.random.rand(dof)
            result = flex_filter.update(new_data)
            # print("new_data:", np.around(new_data, decimals=3))
            # print("result  :", np.around(result, decimals=3))
            self.assertEqual(len(result), dof)
    
    def test_weight_average_one(self):
        dof = 1
        window_size = 20
        flex_filter = Flex(dof, method='weight_average', window_size=window_size)
        for _ in range(30):
            new_data = np.random.rand(dof)
            result = flex_filter.update(new_data)
            # print("new_data:", np.around(new_data, decimals=3))
            # print("result  :", np.around(result, decimals=3))
            self.assertEqual(len(result), dof)

    def test_weight_average_increase(self):
        dof = 3
        cycle = 30
        flex_filter = Flex(dof)
        matrix = []
        for i in range(cycle):
            row = [float(x) for x in [i, i, i]]
            matrix.append(row)
        for i in range(cycle):
            new_data = matrix[i]
            result = flex_filter.update(new_data)
            print("new_data:", np.around(new_data, decimals=3))
            print("result  :", np.around(result, decimals=3))
            self.assertEqual(len(result), dof)


if __name__ == '__main__':
    unittest.main()