import time
import threading
import queue
import numpy as np

class MotionInterpolator:
    def __init__(self, dof, freq):
        self.dof = dof
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.last_waypoint = None
        self.last_time = None
        self.last_output = np.zeros(dof)
        self.running = False
        self.lock = threading.Lock()
        self.interp_thread = threading.Thread(target=self._interpolation_worker)
        self.freq = freq

    def start(self):
        self.running = True
        self.interp_thread.start()

    def stop(self):
        self.running = False
        self.interp_thread.join()

    def add_waypoint(self, waypoint):
        with self.lock:
            self.input_queue.put(np.array(waypoint))
        # self.cal_interpolation()


    def _quintic_interpolation(self, p0, p1, t, T):
        t /= T
        # return p0 + (p1 - p0) * (10*t**3 - 15*t**4 + 6*t**5)
        return p0 + (p1 - p0) * (t)

    def cal_interpolation(self):
        new_waypoint = self.input_queue.get(timeout=0.01)
        current_time = time.time()

        if self.last_waypoint is not None:
            dt = current_time - self.last_time
            num_points = max(1, int(round(dt * self.freq)))
            actual_dt = num_points * (1/self.freq)
            for i in range(1, num_points+1):
                t = i * (1/self.freq)
                interp_point = self._quintic_interpolation(
                    self.last_waypoint,
                    new_waypoint,
                    t,
                    actual_dt
                )
                self.output_queue.put(interp_point)
        else:
            self.last_waypoint = new_waypoint
            self.last_time = current_time

    def _interpolation_worker(self):
        while self.running:
            try:
                new_waypoint = self.input_queue.get(timeout=0.01)
                current_time = time.time()

                if self.last_waypoint is not None:
                    dt = current_time - self.last_time
                    num_points = max(1, int(round(dt * self.freq)))
                    actual_dt = num_points * (1/self.freq)

                    for i in range(1, num_points+1):
                        t = i * (1/self.freq)
                        interp_point = self._quintic_interpolation(
                            self.last_waypoint,
                            new_waypoint,
                            t,
                            actual_dt
                        )
                        self.output_queue.put(interp_point)

                with self.lock:
                    self.last_waypoint = new_waypoint
                    self.last_time = current_time

            except queue.Empty:
                time.sleep(0.005)
                continue